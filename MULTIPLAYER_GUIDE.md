# 联机嘴炮游戏使用指南

## 概述

这是一个基于 ReactTogether 的联机版嘴炮游戏，支持多人实时同步游戏。

## 功能特色

### 🎮 游戏模式

- **单人模式**: 经典的单机嘴炮游戏体验
- **联机模式**: 支持最多 4 人同时游戏的联机模式
- **联机测试**: 简单的消息同步测试，验证连接状态

### 🌐 联机功能

- 实时状态同步
- 多人房间系统
- 玩家准备状态管理
- 实时消息通信

## 技术实现

### 使用的技术栈

- **React 19**: 前端框架
- **TypeScript**: 类型安全
- **ReactTogether (@croquet/react)**: 实时同步库
- **Vite**: 构建工具

### API 配置

- **服务提供商**: multisynq.io
- **API Key**: 2b599LeFw5HNytHLfolNXnHvK7VkXFdUm23jxmC2Z3
- **应用 ID**: com.mouthgame.multiplayer

## 使用方法

### 启动应用

```bash
npm run dev
```

### 测试联机功能

#### 1. 联机测试模式

1. 在主菜单选择"🧪 联机测试"
2. 输入玩家名称并点击"加入游戏"
3. 发送消息测试实时同步
4. 在多个浏览器窗口中打开相同页面测试多人同步

#### 2. 联机游戏模式

1. 在主菜单选择"👥 联机模式"
2. 输入玩家名称并点击"加入游戏"
3. 等待另一名玩家加入（双人游戏）
4. 所有玩家点击"准备"按钮
5. 当两人都准备好后，点击"开始游戏"

### 游戏规则

#### 单机模式

1. 观看第一排小牛马的示例表演
2. 倒计时后，第二排开始模仿
3. 按住空格键控制你的小牛马张嘴
4. 跟随示例的节奏，准确模仿张嘴时机和时长
5. 每个玩家有 3 次生命，出错会扣除生命

#### 联机模式（双人游戏）

1. **房间系统**: 每个房间最多支持 2 名玩家
2. **随机分配**: 每个玩家进入房间时随机分配一个小马（1-5 号位置）
3. **玩家标识**: 小马下方显示玩家昵称，不再显示"YOU"
4. **双人协作**:
   - 两名玩家必须都准备好才能开始游戏
   - 游戏规则与单机版相同，但第二排由两个玩家分别控制
   - 只有当所有存活玩家都通过当前关卡才能进入下一关
5. **生命系统**:
   - 每个玩家有 3 次生命
   - 当一个玩家血量为 0 时，该玩家被淘汰（显示 💀 标记和灰色效果）
   - 当所有玩家都被淘汰时，游戏结束
6. **胜利条件**: 完成最后一关或所有玩家都被淘汰时游戏结束

## 文件结构

```
src/
├── components/
│   ├── GameModeSelector.tsx      # 游戏模式选择器
│   ├── MultiplayerTest.tsx       # 联机测试组件
│   ├── MultiplayerMusicGame.tsx  # 联机游戏组件
│   ├── MusicGameNew.tsx          # 单机游戏组件
│   ├── PersonRow.tsx             # 小牛马行组件
│   └── Person.tsx                # 单个小牛马组件
├── hooks/
│   ├── useGameLogic.ts           # 游戏逻辑钩子
│   ├── useKeyboardControl.ts     # 键盘控制钩子
│   └── ...
├── data/
│   └── levels.ts                 # 关卡数据
└── types/
    └── game.ts                   # 游戏类型定义
```

## 状态同步

### 共享状态

- **消息列表**: 实时同步所有玩家的消息
- **玩家列表**: 在线玩家信息和状态
- **游戏状态**: 当前关卡、游戏阶段等
- **房间状态**: 房间配置和玩家准备状态

### 本地状态

- **玩家输入**: 消息输入框内容
- **UI 状态**: 界面显示状态
- **动画状态**: 小牛马动画状态

## 测试建议

### 单人测试

1. 测试游戏模式切换
2. 验证单机游戏功能正常
3. 检查界面响应和动画

### 多人测试

1. 在多个浏览器窗口中打开应用
2. 测试玩家加入/离开功能
3. 验证消息实时同步
4. 测试游戏状态同步
5. 检查准备状态管理

### 网络测试

1. 在不同设备上打开应用
2. 测试跨设备同步
3. 验证网络断开重连功能

## 已知限制

1. **React 版本兼容性**: ReactTogether 目前只支持 React 18，使用 --legacy-peer-deps 安装
2. **房间人数**: 当前限制双人游戏（2 人）
3. **网络依赖**: 需要稳定的网络连接才能正常同步
4. **游戏逻辑**: 目前联机模式的游戏逻辑还在完善中，主要实现了房间管理和状态同步

## 下一步改进

1. **游戏逻辑完善**: 完整实现多人游戏的计分和胜负判定
2. **键盘控制同步**: 实现多人键盘输入的实时同步
3. **房间管理**: 添加房间创建、加入特定房间等功能
4. **用户体验**: 添加音效、更好的动画效果
5. **错误处理**: 完善网络错误和重连机制

## 故障排除

### 常见问题

1. **连接失败**: 检查网络连接和 API Key 配置
2. **状态不同步**: 刷新页面重新连接
3. **玩家列表异常**: 清除浏览器缓存重试

### 调试方法

1. 打开浏览器开发者工具查看控制台错误
2. 检查网络请求是否正常
3. 验证 ReactTogether 连接状态

## 联系信息

如有问题或建议，请通过以下方式联系：

- 项目仓库: [GitHub 链接]
- 技术支持: [联系方式]
