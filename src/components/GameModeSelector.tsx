import React, { useState } from 'react'
import MusicGame from './MusicGameNew'
import MultiplayerMusicGame from './MultiplayerMusicGame'
import MultiplayerTest from './MultiplayerTest'

type GameMode = 'menu' | 'single' | 'multiplayer' | 'test'

function GameModeSelector() {
  const [gameMode, setGameMode] = useState<GameMode>('menu')

  const containerStyle: React.CSSProperties = {
    minHeight: '100vh',
    backgroundColor: '#f0f9ff',
    fontFamily: 'Arial, sans-serif'
  }

  const menuStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: '100vh',
    padding: '20px'
  }

  const titleStyle: React.CSSProperties = {
    fontSize: '48px',
    fontWeight: 'bold',
    color: '#1e40af',
    marginBottom: '20px',
    textAlign: 'center',
    textShadow: '2px 2px 4px rgba(0,0,0,0.1)'
  }

  const subtitleStyle: React.CSSProperties = {
    fontSize: '18px',
    color: '#64748b',
    marginBottom: '40px',
    textAlign: 'center',
    maxWidth: '600px',
    lineHeight: '1.6'
  }

  const buttonContainerStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    gap: '20px',
    alignItems: 'center'
  }

  const buttonStyle: React.CSSProperties = {
    padding: '15px 30px',
    fontSize: '18px',
    fontWeight: 'bold',
    border: 'none',
    borderRadius: '10px',
    cursor: 'pointer',
    minWidth: '250px',
    transition: 'all 0.3s ease',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
  }

  const singlePlayerButtonStyle: React.CSSProperties = {
    ...buttonStyle,
    backgroundColor: '#3b82f6',
    color: 'white'
  }

  const multiplayerButtonStyle: React.CSSProperties = {
    ...buttonStyle,
    backgroundColor: '#10b981',
    color: 'white'
  }

  const testButtonStyle: React.CSSProperties = {
    ...buttonStyle,
    backgroundColor: '#f59e0b',
    color: 'white'
  }

  const backButtonStyle: React.CSSProperties = {
    position: 'fixed',
    top: '20px',
    left: '20px',
    padding: '10px 20px',
    backgroundColor: '#6b7280',
    color: 'white',
    border: 'none',
    borderRadius: '5px',
    cursor: 'pointer',
    fontSize: '14px',
    fontWeight: 'bold',
    zIndex: 1000
  }

  const featureListStyle: React.CSSProperties = {
    marginTop: '30px',
    textAlign: 'left',
    maxWidth: '500px'
  }

  const featureItemStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    marginBottom: '10px',
    fontSize: '16px',
    color: '#374151'
  }

  // 渲染主菜单
  if (gameMode === 'menu') {
    return (
      <div style={containerStyle}>
        <div style={menuStyle}>
          <h1 style={titleStyle}>🎮 一起来嘴炮</h1>
          <p style={subtitleStyle}>
            欢迎来到嘴炮游戏！这是一个考验节奏感和反应能力的音乐游戏。
            观看示例表演，然后按照节奏控制你的小牛马张嘴。
          </p>

          <div style={buttonContainerStyle}>
            <button
              style={singlePlayerButtonStyle}
              onClick={() => setGameMode('single')}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)'
                e.currentTarget.style.boxShadow = '0 6px 12px rgba(0, 0, 0, 0.15)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)'
                e.currentTarget.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1)'
              }}
            >
              🎯 单人模式
            </button>

            <button
              style={multiplayerButtonStyle}
              onClick={() => setGameMode('multiplayer')}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)'
                e.currentTarget.style.boxShadow = '0 6px 12px rgba(0, 0, 0, 0.15)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)'
                e.currentTarget.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1)'
              }}
            >
              👥 联机模式
            </button>

            <button
              style={testButtonStyle}
              onClick={() => setGameMode('test')}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)'
                e.currentTarget.style.boxShadow = '0 6px 12px rgba(0, 0, 0, 0.15)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)'
                e.currentTarget.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1)'
              }}
            >
              🧪 联机测试
            </button>
          </div>

          <div style={featureListStyle}>
            <h3 style={{ color: '#1e40af', marginBottom: '15px' }}>游戏特色：</h3>
            <div style={featureItemStyle}>
              <span style={{ marginRight: '10px' }}>🎵</span>
              20个精心设计的关卡，难度逐渐提升
            </div>
            <div style={featureItemStyle}>
              <span style={{ marginRight: '10px' }}>🏆</span>
              称号系统，从"小笨嘴"到"传说嘴神"
            </div>
            <div style={featureItemStyle}>
              <span style={{ marginRight: '10px' }}>🎮</span>
              简单的空格键操作，易学难精
            </div>
            <div style={featureItemStyle}>
              <span style={{ marginRight: '10px' }}>👥</span>
              支持多人联机，与朋友一起挑战
            </div>
            <div style={featureItemStyle}>
              <span style={{ marginRight: '10px' }}>🎨</span>
              可爱的小牛马角色和流畅动画
            </div>
          </div>
        </div>
      </div>
    )
  }

  // 渲染游戏界面
  return (
    <div style={containerStyle}>
      {gameMode !== 'menu' && (
        <button
          style={backButtonStyle}
          onClick={() => setGameMode('menu')}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = '#4b5563'
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = '#6b7280'
          }}
        >
          ← 返回主菜单
        </button>
      )}

      {gameMode === 'single' && <MusicGame />}
      {gameMode === 'multiplayer' && <MultiplayerMusicGame />}
      {gameMode === 'test' && <MultiplayerTest />}
    </div>
  )
}

export default GameModeSelector
