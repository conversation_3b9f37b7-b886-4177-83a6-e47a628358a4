import React, { useState, useEffect } from 'react'
import { ReactTogether, useStateTogether } from 'react-together'
import PersonRow from './PersonRow'
import { gameLevels } from '../data/levels'

// 多人游戏状态接口
interface MultiplayerGameState {
  currentLevel: number
  gamePhase:
    | 'waiting'
    | 'demo'
    | 'countdown'
    | 'playing'
    | 'result'
    | 'gameOver'
  demoStartTime: number
  playStartTime: number
  currentTime: number
}

// 玩家状态接口
interface PlayerState {
  id: string
  name: string
  lives: number
  score: number
  playerIndex: number
  isPlayerMouthOpen: boolean
  isReady: boolean
}

// 房间状态接口
interface RoomState {
  players: PlayerState[]
  gameState: MultiplayerGameState
  maxPlayers: number
}

// 称号系统
const getTitleByScore = (score: number): string => {
  if (score < 50) return '小笨嘴'
  if (score < 100) return '小嘴抹了蜜'
  if (score < 150) return '意大利嘴炮'
  if (score < 200) return '嘴强王者'
  return '传说嘴神'
}

function MultiplayerMusicGameContent() {
  const [playerName, setPlayerName] = useState('')
  const [playerId] = useState(() => Math.random().toString(36).substr(2, 9))

  // 共享状态
  const [roomState, setRoomState] = useStateTogether<RoomState>('roomState', {
    players: [],
    gameState: {
      currentLevel: 0,
      gamePhase: 'waiting',
      demoStartTime: 0,
      playStartTime: 0,
      currentTime: 0
    },
    maxPlayers: 4
  })

  // 本地状态
  const [demoPersons, setDemoPersons] = useState<any[]>([])
  const [playPersons, setPlayPersons] = useState<any[]>([])
  const [isJoined, setIsJoined] = useState(false)

  // 获取当前玩家
  const currentPlayer = roomState.players.find(p => p.id === playerId)
  const isRoomFull = roomState.players.length >= roomState.maxPlayers

  // 加入游戏
  const joinGame = () => {
    if (!playerName.trim() || isRoomFull) return

    const newPlayer: PlayerState = {
      id: playerId,
      name: playerName,
      lives: 3,
      score: 0,
      playerIndex: roomState.players.length,
      isPlayerMouthOpen: false,
      isReady: false
    }

    setRoomState(prev => ({
      ...prev,
      players: [...prev.players, newPlayer]
    }))
    setIsJoined(true)
  }

  // 离开游戏
  const leaveGame = () => {
    setRoomState(prev => ({
      ...prev,
      players: prev.players.filter(p => p.id !== playerId)
    }))
    setIsJoined(false)
  }

  // 准备/取消准备
  const toggleReady = () => {
    if (!currentPlayer) return

    setRoomState(prev => ({
      ...prev,
      players: prev.players.map(p =>
        p.id === playerId ? { ...p, isReady: !p.isReady } : p
      )
    }))
  }

  // 开始游戏
  const startGame = () => {
    if (roomState.players.length === 0) return

    setRoomState(prev => ({
      ...prev,
      gameState: {
        ...prev.gameState,
        gamePhase: 'demo',
        demoStartTime: Date.now(),
        currentLevel: 0
      }
    }))
  }

  // 初始化小牛马状态
  const initializePersons = () => {
    const persons = Array.from({ length: 5 }, (_, index) => ({
      index,
      isMouthOpen: false,
      isPlayer: false
    }))
    return persons
  }

  // 更新小牛马状态
  useEffect(() => {
    setDemoPersons(initializePersons())
    setPlayPersons(
      initializePersons().map((person, index) => ({
        ...person,
        isPlayer: roomState.players.some(p => p.playerIndex === index)
      }))
    )
  }, [roomState.players])

  // 样式定义
  const containerStyle: React.CSSProperties = {
    padding: '20px',
    maxWidth: '1200px',
    margin: '0 auto',
    fontFamily: 'Arial, sans-serif'
  }

  const headerStyle: React.CSSProperties = {
    textAlign: 'center',
    marginBottom: '20px'
  }

  const playersListStyle: React.CSSProperties = {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
    gap: '15px',
    marginBottom: '20px'
  }

  const playerCardStyle: React.CSSProperties = {
    border: '2px solid #ddd',
    borderRadius: '8px',
    padding: '15px',
    backgroundColor: '#f9f9f9'
  }

  const readyPlayerStyle: React.CSSProperties = {
    ...playerCardStyle,
    borderColor: '#28a745',
    backgroundColor: '#d4edda'
  }

  const buttonStyle: React.CSSProperties = {
    padding: '10px 20px',
    margin: '5px',
    border: 'none',
    borderRadius: '5px',
    cursor: 'pointer',
    fontSize: '14px',
    fontWeight: 'bold'
  }

  const primaryButtonStyle: React.CSSProperties = {
    ...buttonStyle,
    backgroundColor: '#007bff',
    color: 'white'
  }

  const successButtonStyle: React.CSSProperties = {
    ...buttonStyle,
    backgroundColor: '#28a745',
    color: 'white'
  }

  const dangerButtonStyle: React.CSSProperties = {
    ...buttonStyle,
    backgroundColor: '#dc3545',
    color: 'white'
  }

  // 渲染等待界面
  if (!isJoined) {
    return (
      <div style={containerStyle}>
        <h1 style={headerStyle}>联机嘴炮游戏</h1>

        <div style={{ textAlign: 'center', marginBottom: '30px' }}>
          <div style={{ marginBottom: '20px' }}>
            <input
              type="text"
              value={playerName}
              onChange={e => setPlayerName(e.target.value)}
              placeholder="输入你的名称"
              style={{
                padding: '10px',
                fontSize: '16px',
                border: '2px solid #ddd',
                borderRadius: '5px',
                marginRight: '10px'
              }}
            />
            <button
              onClick={joinGame}
              disabled={!playerName.trim() || isRoomFull}
              style={primaryButtonStyle}
            >
              {isRoomFull ? '房间已满' : '加入游戏'}
            </button>
          </div>
        </div>

        <div style={playersListStyle}>
          {roomState.players.map(player => (
            <div
              key={player.id}
              style={player.isReady ? readyPlayerStyle : playerCardStyle}
            >
              <h4>🎮 {player.name}</h4>
              <p>生命: ❤️ {player.lives}</p>
              <p>积分: ⭐ {player.score}</p>
              <p>状态: {player.isReady ? '✅ 已准备' : '⏳ 等待中'}</p>
            </div>
          ))}

          {/* 显示空位 */}
          {Array.from({
            length: roomState.maxPlayers - roomState.players.length
          }).map((_, index) => (
            <div
              key={`empty-${index}`}
              style={{ ...playerCardStyle, opacity: 0.5 }}
            >
              <h4>空位 {roomState.players.length + index + 1}</h4>
              <p>等待玩家加入...</p>
            </div>
          ))}
        </div>

        <div style={{ textAlign: 'center', marginTop: '20px' }}>
          <p>
            房间人数: {roomState.players.length} / {roomState.maxPlayers}
          </p>
          <p>
            准备人数: {roomState.players.filter(p => p.isReady).length} /{' '}
            {roomState.players.length}
          </p>
        </div>
      </div>
    )
  }

  // 渲染游戏界面
  return (
    <div style={containerStyle}>
      <h1 style={headerStyle}>联机嘴炮游戏</h1>

      {/* 玩家状态栏 */}
      <div style={playersListStyle}>
        {roomState.players.map(player => (
          <div
            key={player.id}
            style={player.isReady ? readyPlayerStyle : playerCardStyle}
          >
            <h4>
              🎮 {player.name} {player.id === playerId && '(你)'}
            </h4>
            <p>生命: ❤️ {player.lives}</p>
            <p>积分: ⭐ {player.score}</p>
            <p>称号: {getTitleByScore(player.score)}</p>
            <p>状态: {player.isReady ? '✅ 已准备' : '⏳ 等待中'}</p>
          </div>
        ))}
      </div>

      {/* 游戏控制按钮 */}
      <div style={{ textAlign: 'center', marginBottom: '20px' }}>
        {currentPlayer && (
          <button
            onClick={toggleReady}
            style={
              currentPlayer.isReady ? dangerButtonStyle : successButtonStyle
            }
          >
            {currentPlayer.isReady ? '取消准备' : '准备'}
          </button>
        )}

        {roomState.players.every(p => p.isReady) &&
          roomState.players.length > 0 && (
            <button onClick={startGame} style={primaryButtonStyle}>
              开始游戏
            </button>
          )}

        <button onClick={leaveGame} style={dangerButtonStyle}>
          离开房间
        </button>
      </div>

      {/* 游戏区域 */}
      {roomState.gameState.gamePhase !== 'waiting' && (
        <div style={{ marginTop: '30px' }}>
          <div style={{ textAlign: 'center', marginBottom: '20px' }}>
            <h3>
              关卡 {roomState.gameState.currentLevel + 1} / {gameLevels.length}
            </h3>
            <p>游戏状态: {roomState.gameState.gamePhase}</p>
          </div>

          <PersonRow persons={demoPersons} label="示例表演" />
          <PersonRow persons={playPersons} label="模仿表演" />

          <div style={{ textAlign: 'center', marginTop: '20px' }}>
            {roomState.gameState.gamePhase === 'demo' && (
              <p>请仔细观看示例表演，记住节奏！</p>
            )}
            {roomState.gameState.gamePhase === 'countdown' && (
              <p>准备好！按住空格键控制你的小牛马张嘴</p>
            )}
            {roomState.gameState.gamePhase === 'playing' && (
              <p>按住空格键控制你的小牛马张嘴！</p>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

// 使用 ReactTogether 包装的组件
function MultiplayerMusicGame() {
  return (
    <ReactTogether
      sessionParams={{
        appId: 'com.mouthgame.multiplayer',
        apiKey: '2b599LeFw5HNytHLfolNXnHvK7VkXFdUm23jxmC2Z3',
        name: 'mouthgame-room',
        password: 'game123'
      }}
    >
      <MultiplayerMusicGameContent />
    </ReactTogether>
  )
}

export default MultiplayerMusicGame
