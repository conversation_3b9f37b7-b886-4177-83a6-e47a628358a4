import React, { useState, useEffect } from 'react'
import { ReactTogether, useStateTogether } from 'react-together'
import PersonRow from './PersonRow'
import { multiplayerLevels } from '../data/multiplayerLevels'
import { useBackgroundMusic } from '../hooks/useBackgroundMusic'

// 添加CSS动画
const pulseAnimation = `
  @keyframes pulse {
    0% { transform: scale(1); }
    100% { transform: scale(1.1); }
  }
`

// 将动画样式注入到页面
if (typeof document !== 'undefined') {
  const style = document.createElement('style')
  style.textContent = pulseAnimation
  document.head.appendChild(style)
}

// 多人游戏状态接口
interface MultiplayerGameState {
  currentLevel: number
  gamePhase: 'waiting' | 'demo' | 'countdown' | 'playing' | 'gameOver'
  demoStartTime: number
  playStartTime: number
  currentTime: number
  winner?: string // 获胜者
}

// 玩家状态接口
interface PlayerState {
  id: string
  name: string
  lives: number
  score: number
  playerIndex: number
  isPlayerMouthOpen: boolean
  isReady: boolean
}

// 房间状态接口
interface RoomState {
  players: PlayerState[]
  gameState: MultiplayerGameState
  maxPlayers: number
  availableHorseIndices: number[] // 可用的小马索引
}

// 称号系统
const getTitleByScore = (score: number): string => {
  if (score < 50) return '小笨嘴'
  if (score < 100) return '小嘴抹了蜜'
  if (score < 150) return '意大利嘴炮'
  if (score < 200) return '嘴强王者'
  return '传说嘴神'
}

function MultiplayerMusicGameContent() {
  const [playerName, setPlayerName] = useState('')
  const [playerId] = useState(() => Math.random().toString(36).substring(2, 11))

  // 共享状态
  const [roomState, setRoomState] = useStateTogether<RoomState>(
    'multiplayerRoomState',
    {
      players: [],
      gameState: {
        currentLevel: 0,
        gamePhase: 'waiting',
        demoStartTime: 0,
        playStartTime: 0,
        currentTime: 0
      },
      maxPlayers: 2, // 改为双人游戏
      availableHorseIndices: [0, 1, 2, 3, 4] // 5个小马位置
    }
  )

  // 调试信息
  console.log('roomState:', roomState)
  console.log('availableHorseIndices:', roomState?.availableHorseIndices)

  // 本地状态
  const [demoPersons, setDemoPersons] = useState<any[]>([])
  const [playPersons, setPlayPersons] = useState<any[]>([])
  const [isJoined, setIsJoined] = useState(false)

  // 音频支持
  const {
    isPlaying: isBGMPlaying,
    isLoaded: isBGMLoaded,
    error: bgmError,
    isMuted,
    volume,
    startBGM,
    stopBGM,
    toggleMute,
    setVolume
  } = useBackgroundMusic()

  // 获取当前玩家
  const currentPlayer = roomState.players.find(p => p.id === playerId)
  const isRoomFull = roomState.players.length >= roomState.maxPlayers

  // 音频控制
  useEffect(() => {
    console.log('游戏状态变化:', {
      gamePhase: roomState.gameState.gamePhase,
      isBGMLoaded,
      isBGMPlaying,
      bgmError
    })
  }, [roomState.gameState.gamePhase, isBGMLoaded, isBGMPlaying, bgmError])

  // 处理用户首次交互，启动音乐
  const handleUserInteraction = async () => {
    setUserHasInteracted(true)
    if (isBGMLoaded && !isBGMPlaying && !isMuted) {
      await startBGM()
    }
  }

  // 启动音乐的函数
  const handleStartMusic = async () => {
    setShowMusicPrompt(false)
    setUserHasInteracted(true)
    if (isBGMLoaded && !isMuted) {
      await startBGM()
    }
  }

  // 游戏结束时停止背景音乐
  useEffect(() => {
    if (roomState.gameState.gamePhase === 'gameOver') {
      stopBGM()
    }
  }, [roomState.gameState.gamePhase, stopBGM])

  // 移除重复的键盘控制（已在下方有更完整的版本）

  // 加入游戏
  const joinGame = () => {
    console.log('=== 联机游戏加入 ===')
    console.log('playerName:', playerName)
    console.log('isRoomFull:', isRoomFull)
    console.log('roomState:', roomState)
    console.log('availableHorseIndices:', roomState?.availableHorseIndices)

    if (!playerName.trim()) {
      alert('请输入玩家名称')
      return
    }

    if (isRoomFull) {
      alert('房间已满')
      return
    }

    if (
      !roomState.availableHorseIndices ||
      roomState.availableHorseIndices.length === 0
    ) {
      alert('没有可用的小马位置')
      return
    }

    // 随机选择一个可用的小马索引
    const randomIndex = Math.floor(
      Math.random() * roomState.availableHorseIndices.length
    )
    const assignedHorseIndex = roomState.availableHorseIndices[randomIndex]
    console.log('分配的小马索引:', assignedHorseIndex)

    const newPlayer: PlayerState = {
      id: playerId,
      name: playerName,
      lives: 3,
      score: 0,
      playerIndex: assignedHorseIndex,
      isPlayerMouthOpen: false,
      isReady: false
    }

    setRoomState(prev => ({
      ...prev,
      players: [...prev.players, newPlayer],
      availableHorseIndices: (prev.availableHorseIndices || []).filter(
        index => index !== assignedHorseIndex
      )
    }))
    setIsJoined(true)
  }

  // 离开游戏
  const leaveGame = () => {
    if (!currentPlayer) return

    setRoomState(prev => ({
      ...prev,
      players: prev.players.filter(p => p.id !== playerId),
      availableHorseIndices: [
        ...(prev.availableHorseIndices || []),
        currentPlayer.playerIndex
      ].sort()
    }))
    setIsJoined(false)
  }

  // 准备/取消准备
  const toggleReady = () => {
    if (!currentPlayer) return

    setRoomState(prev => ({
      ...prev,
      players: prev.players.map(p =>
        p.id === playerId ? { ...p, isReady: !p.isReady } : p
      )
    }))
  }

  // 开始游戏
  const startGame = async () => {
    if (roomState.players.length !== 2) return // 必须双人才能开始

    // 启动音乐
    await handleUserInteraction()

    // 初始化演示小牛马
    const currentLevel = multiplayerLevels[0]
    const newDemoPersons = Array.from({ length: 5 }, (_, index) => ({
      index,
      isMouthOpen: false,
      isPlayer: false
    }))

    // 初始化玩家小牛马
    const newPlayPersons = Array.from({ length: 5 }, (_, index) => {
      const player = roomState.players.find(p => p.playerIndex === index)
      return {
        index,
        isMouthOpen: false,
        isPlayer: !!player,
        playerName: player?.name || '',
        isAlive: player ? player.lives > 0 : true
      }
    })

    setDemoPersons(newDemoPersons)
    setPlayPersons(newPlayPersons)

    setRoomState(prev => ({
      ...prev,
      gameState: {
        ...prev.gameState,
        gamePhase: 'demo',
        demoStartTime: Date.now(),
        currentLevel: 0
      }
    }))

    // 开始演示动画
    console.log('开始演示动画，关卡数据:', currentLevel)
    startDemoAnimation(currentLevel)
  }

  // 演示动画
  const startDemoAnimation = (level: any) => {
    console.log('startDemoAnimation 被调用，level:', level)

    if (!level || !level.actions) {
      console.error('关卡数据无效:', level)
      return
    }

    console.log('开始播放演示动画，actions:', level.actions)

    // 为每个动作创建开始和结束事件
    const events: Array<{
      time: number
      personIndex: number
      action: 'open' | 'close'
    }> = []

    level.actions.forEach((action: any) => {
      events.push({
        time: action.startTime,
        personIndex: action.personIndex,
        action: 'open'
      })
      events.push({
        time: action.startTime + action.duration,
        personIndex: action.personIndex,
        action: 'close'
      })
    })

    // 按时间排序
    events.sort((a, b) => a.time - b.time)
    console.log('生成的事件序列:', events)

    // 执行所有事件
    events.forEach((event, index) => {
      setTimeout(() => {
        console.log(`执行事件 ${index}:`, event)

        setDemoPersons(prev => {
          const newPersons = prev.map((person, i) => ({
            ...person,
            isMouthOpen:
              i === event.personIndex
                ? event.action === 'open'
                : person.isMouthOpen
          }))
          console.log('更新演示小牛马状态:', newPersons)
          return newPersons
        })
      }, event.time)
    })

    // 演示结束后显示GO过渡
    setTimeout(() => {
      console.log('🎬 演示动画结束，显示GO过渡，关卡时长:', level.totalDuration)
      setRoomState(prev => ({
        ...prev,
        gameState: {
          ...prev.gameState,
          gamePhase: 'countdown'
        }
      }))

      // GO过渡后开始游戏
      setTimeout(() => {
        console.log('🚀 GO过渡结束，开始游戏')
        setRoomState(prev => ({
          ...prev,
          gameState: {
            ...prev.gameState,
            gamePhase: 'playing',
            playStartTime: Date.now()
          }
        }))
      }, 2000) // GO显示2秒
    }, level.totalDuration + 500) // 演示结束后等待500ms
  }

  // 删除重复的函数定义

  // 初始化小牛马状态
  const initializePersons = () => {
    const persons = Array.from({ length: 5 }, (_, index) => ({
      index,
      isMouthOpen: false,
      isPlayer: false
    }))
    return persons
  }

  // 第二排小马状态 - 包含电脑控制的小马
  const [computerMouthStates, setComputerMouthStates] = useState<boolean[]>([
    false,
    false,
    false,
    false,
    false
  ])

  // 更新小牛马状态
  useEffect(() => {
    setDemoPersons(initializePersons())

    // 第二排显示所有5个小马：玩家控制的 + 电脑控制的
    setPlayPersons(
      initializePersons().map((person, index) => {
        const player = roomState.players.find(p => p.playerIndex === index)
        return {
          ...person,
          isPlayer: !!player, // 标记是否为玩家控制
          playerName: player?.name || '', // 玩家名称
          isAlive: player ? player.lives > 0 : true, // 存活状态
          isMouthOpen: player
            ? player.isPlayerMouthOpen || false // 玩家控制的小马
            : computerMouthStates[index] // 电脑控制的小马
        }
      })
    )
  }, [roomState.players, computerMouthStates])

  // 游戏结果判定
  useEffect(() => {
    if (roomState.gameState.gamePhase === 'playing') {
      // 检查是否有玩家血量为0
      const deadPlayers = roomState.players.filter(p => p.lives <= 0)
      if (deadPlayers.length > 0) {
        // 有玩家死亡，游戏结束
        const alivePlayers = roomState.players.filter(p => p.lives > 0)
        setRoomState(prev => ({
          ...prev,
          gameState: {
            ...prev.gameState,
            gamePhase: 'gameOver',
            winner: alivePlayers.length > 0 ? alivePlayers[0].name : undefined
          }
        }))
      }
    }
  }, [roomState.players])

  // 新的游戏逻辑 - 简化的直接处理机制

  // 处理玩家出错 - 直接扣血并重新开始
  const handlePlayerError = (errorPlayerId: string) => {
    console.log(`🚨 玩家 ${errorPlayerId} 出错，扣除1滴血`)

    setRoomState(prev => {
      const updatedPlayers = prev.players.map(p =>
        p.id === errorPlayerId
          ? { ...p, lives: Math.max(0, p.lives - 1), isPlayerMouthOpen: false }
          : { ...p, isPlayerMouthOpen: false }
      )

      // 检查是否有玩家死亡
      const deadPlayers = updatedPlayers.filter(p => p.lives <= 0)

      if (deadPlayers.length > 0) {
        // 有玩家死亡，游戏结束
        const alivePlayers = updatedPlayers.filter(p => p.lives > 0)
        return {
          ...prev,
          players: updatedPlayers,
          gameState: {
            ...prev.gameState,
            gamePhase: 'gameOver',
            winner: alivePlayers.length > 0 ? alivePlayers[0].name : '平局'
          }
        }
      } else {
        // 继续游戏，重新开始当前关卡
        return {
          ...prev,
          players: updatedPlayers,
          gameState: {
            ...prev.gameState,
            gamePhase: 'demo',
            demoStartTime: Date.now(),
            levelResult: undefined
          }
        }
      }
    })

    // 如果没有玩家死亡，1秒后重新开始当前关卡
    setTimeout(() => {
      if (roomState.players.every(p => p.lives > 0)) {
        const currentLevel = multiplayerLevels[roomState.gameState.currentLevel]
        startDemoAnimation(currentLevel)
      }
    }, 1000)
  }

  // 处理关卡成功 - 直接进入下一关
  const handleLevelSuccess = () => {
    console.log(`🎉 关卡 ${roomState.gameState.currentLevel + 1} 完成！`)

    const nextLevel = roomState.gameState.currentLevel + 1

    if (nextLevel >= multiplayerLevels.length) {
      // 游戏完成
      setRoomState(prev => ({
        ...prev,
        gameState: {
          ...prev.gameState,
          gamePhase: 'gameOver',
          winner: '双人胜利！'
        }
      }))
    } else {
      // 进入下一关
      setRoomState(prev => ({
        ...prev,
        players: prev.players.map(p => ({
          ...p,
          isPlayerMouthOpen: false,
          score: p.score + 10 // 成功完成关卡加10分
        })),
        gameState: {
          ...prev.gameState,
          currentLevel: nextLevel,
          gamePhase: 'demo',
          demoStartTime: Date.now(),
          levelResult: undefined
        }
      }))

      // 1秒后开始下一关的演示
      setTimeout(() => {
        startDemoAnimation(multiplayerLevels[nextLevel])
      }, 1000)
    }
  }

  // 移除不再使用的函数

  // 键盘控制 - 空格键控制玩家小马（添加防抖机制）
  useEffect(() => {
    if (roomState.gameState.gamePhase !== 'playing' || !currentPlayer) return

    let isKeyPressed = false // 防止重复触发

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.code === 'Space' && !isKeyPressed) {
        event.preventDefault()
        isKeyPressed = true

        console.log(
          `🎮 玩家 ${currentPlayer.name} 按下空格键 (位置: ${currentPlayer.playerIndex})`
        )

        // 更新当前玩家的嘴巴状态为张开
        setRoomState(prev => ({
          ...prev,
          players: prev.players.map(p =>
            p.id === playerId ? { ...p, isPlayerMouthOpen: true } : p
          )
        }))
      }
    }

    const handleKeyUp = (event: KeyboardEvent) => {
      if (event.code === 'Space' && isKeyPressed) {
        event.preventDefault()
        isKeyPressed = false

        console.log(
          `🎮 玩家 ${currentPlayer.name} 松开空格键 (位置: ${currentPlayer.playerIndex})`
        )

        // 更新当前玩家的嘴巴状态为闭合
        setRoomState(prev => ({
          ...prev,
          players: prev.players.map(p =>
            p.id === playerId ? { ...p, isPlayerMouthOpen: false } : p
          )
        }))
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    window.addEventListener('keyup', handleKeyUp)

    return () => {
      window.removeEventListener('keydown', handleKeyDown)
      window.removeEventListener('keyup', handleKeyUp)
    }
  }, [roomState.gameState.gamePhase, currentPlayer, playerId])

  // 新的游戏逻辑检测 - 严格检测张嘴顺序和次数
  useEffect(() => {
    if (roomState.gameState.gamePhase !== 'playing') return

    const currentLevel = multiplayerLevels[roomState.gameState.currentLevel]
    if (!currentLevel) return

    const startTime = roomState.gameState.playStartTime
    if (!startTime) return

    // 为每个玩家创建动作序列跟踪
    const playerActionTrackers = new Map()
    roomState.players.forEach(player => {
      // 找到该玩家需要执行的动作
      const playerActions = currentLevel.actions
        .filter(action => action.personIndex === player.playerIndex)
        .sort((a, b) => a.startTime - b.startTime)

      playerActionTrackers.set(player.id, {
        actions: playerActions,
        currentActionIndex: 0,
        isCurrentlyPressed: false,
        hasError: false
      })
    })

    const checkInterval = setInterval(() => {
      const currentTime = Date.now()
      const elapsedTime = currentTime - startTime

      // 检查关卡是否完成
      if (elapsedTime >= currentLevel.totalDuration + 500) {
        clearInterval(checkInterval)
        handleLevelSuccess()
        return
      }

      // 检查每个玩家的动作执行情况
      roomState.players.forEach(player => {
        const tracker = playerActionTrackers.get(player.id)
        if (!tracker || tracker.hasError) return

        const currentAction = tracker.actions[tracker.currentActionIndex]
        if (!currentAction) return // 该玩家已完成所有动作

        const actionStart = currentAction.startTime
        const actionEnd = currentAction.startTime + currentAction.duration
        const tolerance = 200 // 200ms容错时间
        const isPlayerPressed = player.isPlayerMouthOpen || false

        console.log(`🎯 检测玩家 ${player.name}:`)
        console.log(
          `   当前动作: ${tracker.currentActionIndex + 1}/${
            tracker.actions.length
          }`
        )
        console.log(`   动作时间: ${actionStart}-${actionEnd}ms`)
        console.log(`   当前时间: ${elapsedTime}ms`)
        console.log(`   玩家状态: ${isPlayerPressed ? '张嘴' : '闭嘴'}`)

        // 检测错误情况
        // 1. 在动作时间之前张嘴（提前张嘴）
        if (
          elapsedTime < actionStart - tolerance &&
          isPlayerPressed &&
          !tracker.isCurrentlyPressed
        ) {
          console.log(`❌ 玩家 ${player.name} 提前张嘴！`)
          tracker.hasError = true
          clearInterval(checkInterval)
          handlePlayerError(player.id)
          return
        }

        // 2. 在动作时间内应该张嘴但没张嘴（错过动作）
        if (
          elapsedTime >= actionStart + tolerance &&
          elapsedTime <= actionEnd - tolerance &&
          !isPlayerPressed
        ) {
          console.log(`❌ 玩家 ${player.name} 错过动作！`)
          tracker.hasError = true
          clearInterval(checkInterval)
          handlePlayerError(player.id)
          return
        }

        // 3. 在动作时间之后还在张嘴（张嘴时间过长）
        if (elapsedTime > actionEnd + tolerance && isPlayerPressed) {
          console.log(`❌ 玩家 ${player.name} 张嘴时间过长！`)
          tracker.hasError = true
          clearInterval(checkInterval)
          handlePlayerError(player.id)
          return
        }

        // 4. 在非动作时间张嘴（乱张嘴）
        if (
          (elapsedTime < actionStart - tolerance ||
            elapsedTime > actionEnd + tolerance) &&
          isPlayerPressed &&
          !tracker.isCurrentlyPressed
        ) {
          console.log(`❌ 玩家 ${player.name} 在错误时间张嘴！`)
          tracker.hasError = true
          clearInterval(checkInterval)
          handlePlayerError(player.id)
          return
        }

        // 更新状态
        tracker.isCurrentlyPressed = isPlayerPressed

        // 检查是否完成当前动作
        if (
          elapsedTime > actionEnd &&
          tracker.currentActionIndex < tracker.actions.length - 1
        ) {
          tracker.currentActionIndex++
          console.log(
            `✅ 玩家 ${player.name} 完成动作 ${tracker.currentActionIndex}`
          )
        }
      })
    }, 100) // 提高检测频率到100ms，更精确

    return () => clearInterval(checkInterval)
  }, [roomState.gameState.gamePhase, roomState.gameState.playStartTime])

  // 电脑控制的小马逻辑 - 在游戏阶段完全正确地模仿第一排
  useEffect(() => {
    if (roomState.gameState.gamePhase === 'playing') {
      const currentLevel = multiplayerLevels[roomState.gameState.currentLevel]
      if (!currentLevel) return

      const startTime = roomState.gameState.playStartTime
      if (!startTime) return

      // 为电脑控制的小马创建完全正确的动作序列
      const events: Array<{
        time: number
        personIndex: number
        action: 'open' | 'close'
      }> = []

      currentLevel.actions.forEach((action: any) => {
        events.push({
          time: action.startTime,
          personIndex: action.personIndex,
          action: 'open'
        })
        events.push({
          time: action.startTime + action.duration,
          personIndex: action.personIndex,
          action: 'close'
        })
      })

      // 按时间排序
      events.sort((a, b) => a.time - b.time)

      // 执行所有事件，但只控制非玩家的小马
      events.forEach(event => {
        setTimeout(() => {
          // 检查这个位置是否有玩家控制
          const hasPlayer = roomState.players.some(
            p => p.playerIndex === event.personIndex
          )

          if (!hasPlayer) {
            // 只有非玩家控制的小马才由电脑控制
            setComputerMouthStates(prev => {
              const newStates = [...prev]
              newStates[event.personIndex] = event.action === 'open'
              return newStates
            })
          }
        }, event.time)
      })
    }
  }, [
    roomState.gameState.gamePhase,
    roomState.gameState.playStartTime,
    roomState.players
  ])

  // 监听游戏阶段变化
  useEffect(() => {
    if (roomState.gameState.gamePhase === 'playing') {
      // 记录游戏开始时间，并重置所有状态
      setRoomState(prev => ({
        ...prev,
        players: prev.players.map(p => ({
          ...p,
          isPlayerMouthOpen: false // 重置嘴巴状态
        })),
        gameState: {
          ...prev.gameState,
          playStartTime: Date.now()
        }
      }))

      // 重置电脑控制的小马状态
      setComputerMouthStates([false, false, false, false, false])
    }

    // 在其他阶段也重置状态
    if (
      roomState.gameState.gamePhase === 'demo' ||
      roomState.gameState.gamePhase === 'countdown'
    ) {
      setComputerMouthStates([false, false, false, false, false])
      setRoomState(prev => ({
        ...prev,
        players: prev.players.map(p => ({
          ...p,
          isPlayerMouthOpen: false // 重置嘴巴状态
        }))
      }))
    }
  }, [roomState.gameState.gamePhase])

  // 样式定义 - 采用与单机版相似的左右布局
  const gameContainerStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'row',
    height: '100vh',
    width: '100vw',
    backgroundColor: '#f8f9fa',
    fontFamily: '"Courier New", "Monaco", "Menlo", monospace',
    overflow: 'hidden',
    position: 'relative',
    backgroundImage: `
      linear-gradient(90deg, #e9ecef 1px, transparent 1px),
      linear-gradient(180deg, #e9ecef 1px, transparent 1px)
    `,
    backgroundSize: '20px 20px'
  }

  // 左侧信息面板样式
  const leftPanelStyle: React.CSSProperties = {
    width: '350px',
    minWidth: '350px',
    height: '100vh',
    padding: '20px',
    backgroundColor: '#fff',
    border: '4px solid #000',
    borderTop: 'none',
    borderLeft: 'none',
    borderBottom: 'none',
    display: 'flex',
    flexDirection: 'column',
    gap: '20px',
    overflowY: 'auto'
  }

  // 右侧游戏区域样式
  const rightGameAreaStyle: React.CSSProperties = {
    flex: 1,
    height: '100vh',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    padding: '20px',
    position: 'relative',
    overflow: 'hidden'
  }

  const headerStyle: React.CSSProperties = {
    textAlign: 'center',
    marginBottom: '20px',
    padding: '20px',
    border: '4px solid #303030',
    borderRadius: '0',
    backgroundColor: '#f3f0ed',
    boxShadow: '8px 8px 0px #303030',
    position: 'relative'
  }

  const titleStyle: React.CSSProperties = {
    fontSize: '24px',
    fontWeight: 'bold',
    color: '#303030',
    marginBottom: '10px',
    letterSpacing: '2px',
    textTransform: 'uppercase'
  }

  const playersListStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    gap: '15px'
  }

  const playerCardStyle: React.CSSProperties = {
    border: '3px solid #303030',
    borderRadius: '0',
    padding: '15px',
    backgroundColor: '#f3f0ed',
    boxShadow: '4px 4px 0px #303030',
    marginBottom: '10px'
  }

  const readyPlayerStyle: React.CSSProperties = {
    ...playerCardStyle,
    borderColor: '#28a745',
    backgroundColor: '#d4edda',
    boxShadow: '4px 4px 0px #28a745'
  }

  const buttonStyle: React.CSSProperties = {
    padding: '12px 24px',
    fontSize: '14px',
    fontWeight: 'bold',
    border: '3px solid #303030',
    borderRadius: '0',
    cursor: 'pointer',
    backgroundColor: '#f3f0ed',
    color: '#303030',
    transition: 'all 0.1s ease',
    boxShadow: '4px 4px 0px #303030',
    textTransform: 'uppercase',
    letterSpacing: '1px',
    fontFamily: '"Courier New", "Monaco", "Menlo", monospace',
    margin: '5px'
  }

  const primaryButtonStyle: React.CSSProperties = {
    ...buttonStyle,
    backgroundColor: '#007bff',
    color: 'white',
    borderColor: '#007bff',
    boxShadow: '4px 4px 0px #007bff'
  }

  const successButtonStyle: React.CSSProperties = {
    ...buttonStyle,
    backgroundColor: '#28a745',
    color: 'white',
    borderColor: '#28a745',
    boxShadow: '4px 4px 0px #28a745'
  }

  const dangerButtonStyle: React.CSSProperties = {
    ...buttonStyle,
    backgroundColor: '#dc3545',
    color: 'white',
    borderColor: '#dc3545',
    boxShadow: '4px 4px 0px #dc3545'
  }

  const gameAreaStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: '24px',
    height: '680px',
    width: '100%',
    maxWidth: '800px',
    justifyContent: 'flex-start',
    padding: '40px 20px 20px 20px',
    border: '4px solid #303030',
    borderRadius: '0',
    backgroundColor: '#f3f0ed',
    boxShadow: '8px 8px 0px #303030',
    position: 'relative',
    overflow: 'hidden'
  }

  const instructionStyle: React.CSSProperties = {
    textAlign: 'center',
    fontSize: '16px',
    color: '#303030',
    backgroundColor: '#f3f0ed',
    padding: '16px',
    borderRadius: '0',
    border: '3px solid #303030',
    boxShadow: '6px 6px 0px #303030',
    fontFamily: '"Courier New", "Monaco", "Menlo", monospace',
    fontWeight: 'bold'
  }

  // 渲染等待界面
  if (!isJoined) {
    return (
      <div style={gameContainerStyle}>
        {/* 左侧信息面板 */}
        <div style={leftPanelStyle}>
          <div style={headerStyle}>
            <div style={titleStyle}>联机嘴炮游戏</div>
          </div>

          {/* 加入游戏区域 */}
          <div
            style={{
              backgroundColor: '#f3f0ed',
              border: '3px solid #303030',
              borderRadius: '0',
              padding: '20px',
              boxShadow: '4px 4px 0px #303030'
            }}
          >
            <h3 style={{ color: '#303030', marginBottom: '15px' }}>加入房间</h3>
            <input
              type="text"
              value={playerName}
              onChange={e => setPlayerName(e.target.value)}
              placeholder="输入你的名称"
              style={{
                width: '100%',
                padding: '12px',
                fontSize: '16px',
                border: '3px solid #303030',
                borderRadius: '0',
                marginBottom: '15px',
                fontFamily: '"Courier New", "Monaco", "Menlo", monospace',
                backgroundColor: '#fff',
                boxShadow: '2px 2px 0px #303030'
              }}
            />
            <button
              onClick={joinGame}
              disabled={!playerName.trim() || isRoomFull}
              style={primaryButtonStyle}
            >
              {isRoomFull ? '房间已满' : '加入游戏'}
            </button>
            <button
              onClick={() => {
                console.log('重置联机房间状态')
                setRoomState({
                  players: [],
                  gameState: {
                    currentLevel: 0,
                    gamePhase: 'waiting',
                    demoStartTime: 0,
                    playStartTime: 0,
                    currentTime: 0
                  },
                  maxPlayers: 2,
                  availableHorseIndices: [0, 1, 2, 3, 4]
                })
                alert('房间状态已重置!')
              }}
              style={dangerButtonStyle}
            >
              重置房间
            </button>
          </div>

          {/* 玩家列表 */}
          <div style={playersListStyle}>
            <h3 style={{ color: '#303030', marginBottom: '15px' }}>在线玩家</h3>
            {roomState.players.map(player => (
              <div
                key={player.id}
                style={player.isReady ? readyPlayerStyle : playerCardStyle}
              >
                <h4 style={{ color: '#303030' }}>
                  🐴 {player.name} (小马 {player.playerIndex + 1})
                </h4>
                <p style={{ color: '#303030' }}>生命: ❤️ {player.lives}</p>
                <p style={{ color: '#303030' }}>积分: ⭐ {player.score}</p>
                <p style={{ color: '#303030' }}>
                  状态: {player.isReady ? '✅ 已准备' : '⏳ 等待中'}
                </p>
                {player.lives === 0 && (
                  <p style={{ color: '#dc3545' }}>💀 已淘汰</p>
                )}
              </div>
            ))}

            {/* 显示空位 */}
            {roomState.players.length < roomState.maxPlayers && (
              <div style={{ ...playerCardStyle, opacity: 0.5 }}>
                <h4 style={{ color: '#303030' }}>
                  空位 {roomState.players.length + 1}
                </h4>
                <p style={{ color: '#303030' }}>等待玩家加入...</p>
              </div>
            )}
          </div>

          {/* 房间信息 */}
          <div
            style={{
              backgroundColor: '#f3f0ed',
              border: '3px solid #303030',
              borderRadius: '0',
              padding: '15px',
              boxShadow: '4px 4px 0px #303030'
            }}
          >
            <h3 style={{ color: '#303030', marginBottom: '10px' }}>房间信息</h3>
            <p style={{ color: '#303030', fontSize: '14px' }}>
              <strong>双人游戏模式</strong>
            </p>
            <p style={{ color: '#303030', fontSize: '14px' }}>
              房间人数: {roomState.players.length} / {roomState.maxPlayers}
            </p>
            <p style={{ color: '#303030', fontSize: '14px' }}>
              准备人数: {roomState.players.filter(p => p.isReady).length} /{' '}
              {roomState.players.length}
            </p>
            <p style={{ color: '#303030', fontSize: '12px' }}>
              可用位置:{' '}
              {roomState.availableHorseIndices
                ? roomState.availableHorseIndices.join(', ')
                : '加载中...'}
            </p>
            {roomState.players.length < 2 && (
              <p
                style={{
                  color: '#f59e0b',
                  fontSize: '14px',
                  fontWeight: 'bold'
                }}
              >
                ⚠️ 需要2名玩家才能开始游戏
              </p>
            )}
          </div>
        </div>

        {/* 右侧游戏预览区域 */}
        <div style={rightGameAreaStyle}>
          <div style={gameAreaStyle}>
            <div style={instructionStyle}>
              <h2 style={{ color: '#303030', marginBottom: '15px' }}>
                欢迎来到联机嘴炮游戏！
              </h2>
              <p style={{ color: '#303030' }}>游戏规则：</p>
              <p style={{ color: '#303030' }}>1. 观看第一排小牛马的示例表演</p>
              <p style={{ color: '#303030' }}>2. 倒计时后，第二排开始模仿</p>
              <p style={{ color: '#303030' }}>
                3. 按住空格键控制你的小牛马张嘴
              </p>
              <p style={{ color: '#303030' }}>
                4. 跟随示例的节奏，准确模仿张嘴时机和时长
              </p>
              <p style={{ color: '#303030' }}>
                5. 双人必须都通过才能进入下一关
              </p>
              <p style={{ color: '#303030' }}>
                6. 每个玩家有3次生命，出错会扣除生命
              </p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // 渲染游戏界面
  return (
    <div style={gameContainerStyle}>
      {/* 左侧信息面板 */}
      <div style={leftPanelStyle}>
        <div style={headerStyle}>
          <div style={titleStyle}>联机嘴炮游戏</div>
        </div>

        {/* 玩家状态栏 */}
        <div style={playersListStyle}>
          <h3 style={{ color: '#303030', marginBottom: '15px' }}>游戏中玩家</h3>
          {roomState.players.map(player => (
            <div
              key={player.id}
              style={player.isReady ? readyPlayerStyle : playerCardStyle}
            >
              <h4 style={{ color: '#303030' }}>
                🐴 {player.name} (小马 {player.playerIndex + 1}){' '}
                {player.id === playerId && '(你)'}
              </h4>
              <p style={{ color: '#303030' }}>生命: ❤️ {player.lives}</p>
              <p style={{ color: '#303030' }}>积分: ⭐ {player.score}</p>
              <p style={{ color: '#303030' }}>
                称号: {getTitleByScore(player.score)}
              </p>
              <p style={{ color: '#303030' }}>
                状态: {player.isReady ? '✅ 已准备' : '⏳ 等待中'}
              </p>
              {player.lives === 0 && (
                <p style={{ color: '#dc3545' }}>💀 已淘汰</p>
              )}
            </div>
          ))}
        </div>

        {/* 游戏控制按钮 */}
        <div
          style={{
            backgroundColor: '#f3f0ed',
            border: '3px solid #303030',
            borderRadius: '0',
            padding: '15px',
            boxShadow: '4px 4px 0px #303030'
          }}
        >
          <h3 style={{ color: '#303030', marginBottom: '15px' }}>游戏控制</h3>
          {currentPlayer && (
            <button
              onClick={toggleReady}
              style={
                currentPlayer.isReady ? dangerButtonStyle : successButtonStyle
              }
            >
              {currentPlayer.isReady ? '取消准备' : '准备'}
            </button>
          )}

          {roomState.players.every(p => p.isReady) &&
            roomState.players.length === 2 && (
              <button onClick={startGame} style={primaryButtonStyle}>
                开始游戏
              </button>
            )}

          <button onClick={leaveGame} style={dangerButtonStyle}>
            离开房间
          </button>
        </div>
      </div>

      {/* 右侧游戏区域 */}
      <div style={rightGameAreaStyle}>
        {roomState.gameState.gamePhase === 'gameOver' ? (
          <div style={gameAreaStyle}>
            <div style={instructionStyle}>
              <h2 style={{ color: '#303030', marginBottom: '15px' }}>
                🎉 游戏结束！
              </h2>
              <p
                style={{
                  color: '#303030',
                  fontSize: '18px',
                  marginBottom: '10px'
                }}
              >
                获胜者: {(roomState.gameState as any).winner || '无'}
              </p>
              <button
                onClick={() => {
                  setRoomState(prev => ({
                    ...prev,
                    gameState: {
                      ...prev.gameState,
                      gamePhase: 'waiting',
                      currentLevel: 0,
                      winner: undefined
                    },
                    players: prev.players.map(p => ({
                      ...p,
                      lives: 3,
                      score: 0,
                      isReady: false,
                      isPlayerMouthOpen: false
                    }))
                  }))
                }}
                style={primaryButtonStyle}
              >
                重新开始
              </button>
            </div>
          </div>
        ) : roomState.gameState.gamePhase !== 'waiting' ? (
          <div style={gameAreaStyle}>
            <div style={{ textAlign: 'center', marginBottom: '20px' }}>
              <h3 style={{ color: '#303030' }}>
                关卡 {roomState.gameState.currentLevel + 1} /{' '}
                {multiplayerLevels.length}
              </h3>
              <p style={{ color: '#303030' }}>
                游戏状态: {roomState.gameState.gamePhase}
              </p>
            </div>

            <PersonRow persons={demoPersons} label="示例表演" />
            <PersonRow persons={playPersons} label="模仿表演" />

            <div style={instructionStyle}>
              {roomState.gameState.gamePhase === 'demo' && (
                <p>请仔细观看示例表演，记住节奏！</p>
              )}
              {roomState.gameState.gamePhase === 'countdown' && (
                <div
                  style={{
                    position: 'fixed',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'center',
                    zIndex: 1000
                  }}
                >
                  <div
                    style={{
                      fontSize: '120px',
                      fontWeight: 'bold',
                      color: '#ff6b6b',
                      textShadow: '6px 6px 0px #000',
                      animation: 'pulse 0.8s infinite alternate',
                      textAlign: 'center',
                      padding: '40px',
                      border: '6px solid #ff6b6b',
                      borderRadius: '20px',
                      backgroundColor: '#fff',
                      boxShadow: '12px 12px 0px #ff6b6b',
                      transform: 'scale(1.2)',
                      marginBottom: '20px'
                    }}
                  >
                    GO!
                  </div>
                  <p
                    style={{
                      fontSize: '24px',
                      color: '#fff',
                      fontWeight: 'bold',
                      textShadow: '2px 2px 0px #000'
                    }}
                  >
                    准备开始模仿表演！
                  </p>
                </div>
              )}
              {roomState.gameState.gamePhase === 'playing' && (
                <p>按住空格键控制你的小牛马张嘴！</p>
              )}
              {/* 调试信息 */}
              <p style={{ fontSize: '12px', color: '#999', marginTop: '10px' }}>
                当前阶段: {roomState.gameState.gamePhase} | 关卡:{' '}
                {roomState.gameState.currentLevel + 1}
              </p>
            </div>
          </div>
        ) : (
          <div style={gameAreaStyle}>
            <div style={instructionStyle}>
              <h2 style={{ color: '#303030', marginBottom: '15px' }}>
                等待游戏开始
              </h2>
              <p style={{ color: '#303030' }}>游戏规则：</p>
              <p style={{ color: '#303030' }}>1. 观看第一排小牛马的示例表演</p>
              <p style={{ color: '#303030' }}>2. 倒计时后，第二排开始模仿</p>
              <p style={{ color: '#303030' }}>
                3. 按住空格键控制你的小牛马张嘴
              </p>
              <p style={{ color: '#303030' }}>
                4. 跟随示例的节奏，准确模仿张嘴时机和时长
              </p>
              <p style={{ color: '#303030' }}>
                5. 双人必须都通过才能进入下一关
              </p>
              <p style={{ color: '#303030' }}>
                6. 每个玩家有3次生命，出错会扣除生命
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

// 使用 ReactTogether 包装的组件
function MultiplayerMusicGame() {
  return (
    <ReactTogether
      sessionParams={{
        appId: 'com.mouthgame.multiplayer',
        apiKey: '2b599LeFw5HNytHLfolNXnHvK7VkXFdUm23jxmC2Z3',
        name: 'mouthgame-room',
        password: 'game123'
      }}
    >
      <MultiplayerMusicGameContent />
    </ReactTogether>
  )
}

export default MultiplayerMusicGame
