import React, { useState, useEffect } from 'react'
import { ReactTogether, useStateTogether } from 'react-together'
import PersonRow from './PersonRow'
import { gameLevels } from '../data/levels'
import { useBackgroundMusic } from '../hooks/useBackgroundMusic'

// 多人游戏状态接口
interface MultiplayerGameState {
  currentLevel: number
  gamePhase:
    | 'waiting'
    | 'demo'
    | 'countdown'
    | 'playing'
    | 'result'
    | 'gameOver'
  demoStartTime: number
  playStartTime: number
  currentTime: number
}

// 玩家状态接口
interface PlayerState {
  id: string
  name: string
  lives: number
  score: number
  playerIndex: number
  isPlayerMouthOpen: boolean
  isReady: boolean
}

// 房间状态接口
interface RoomState {
  players: PlayerState[]
  gameState: MultiplayerGameState
  maxPlayers: number
  availableHorseIndices: number[] // 可用的小马索引
}

// 称号系统
const getTitleByScore = (score: number): string => {
  if (score < 50) return '小笨嘴'
  if (score < 100) return '小嘴抹了蜜'
  if (score < 150) return '意大利嘴炮'
  if (score < 200) return '嘴强王者'
  return '传说嘴神'
}

function MultiplayerMusicGameContent() {
  const [playerName, setPlayerName] = useState('')
  const [playerId] = useState(() => Math.random().toString(36).substring(2, 11))

  // 共享状态
  const [roomState, setRoomState] = useStateTogether<RoomState>(
    'multiplayerRoomState',
    {
      players: [],
      gameState: {
        currentLevel: 0,
        gamePhase: 'waiting',
        demoStartTime: 0,
        playStartTime: 0,
        currentTime: 0
      },
      maxPlayers: 2, // 改为双人游戏
      availableHorseIndices: [0, 1, 2, 3, 4] // 5个小马位置
    }
  )

  // 调试信息
  console.log('roomState:', roomState)
  console.log('availableHorseIndices:', roomState?.availableHorseIndices)

  // 本地状态
  const [demoPersons, setDemoPersons] = useState<any[]>([])
  const [playPersons, setPlayPersons] = useState<any[]>([])
  const [isJoined, setIsJoined] = useState(false)
  const [playerActionsRef] = useState({ current: [] as any[] })
  const [showMusicPrompt, setShowMusicPrompt] = useState(true)
  const [userHasInteracted, setUserHasInteracted] = useState(false)

  // 音频支持
  const {
    isPlaying: isBGMPlaying,
    isLoaded: isBGMLoaded,
    error: bgmError,
    isMuted,
    volume,
    startBGM,
    stopBGM,
    toggleMute,
    setVolume
  } = useBackgroundMusic()

  // 获取当前玩家
  const currentPlayer = roomState.players.find(p => p.id === playerId)
  const isRoomFull = roomState.players.length >= roomState.maxPlayers

  // 音频控制
  useEffect(() => {
    console.log('游戏状态变化:', {
      gamePhase: roomState.gameState.gamePhase,
      isBGMLoaded,
      isBGMPlaying,
      bgmError
    })
  }, [roomState.gameState.gamePhase, isBGMLoaded, isBGMPlaying, bgmError])

  // 处理用户首次交互，启动音乐
  const handleUserInteraction = async () => {
    setUserHasInteracted(true)
    if (isBGMLoaded && !isBGMPlaying && !isMuted) {
      await startBGM()
    }
  }

  // 启动音乐的函数
  const handleStartMusic = async () => {
    setShowMusicPrompt(false)
    setUserHasInteracted(true)
    if (isBGMLoaded && !isMuted) {
      await startBGM()
    }
  }

  // 游戏结束时停止背景音乐
  useEffect(() => {
    if (roomState.gameState.gamePhase === 'gameOver') {
      stopBGM()
    }
  }, [roomState.gameState.gamePhase, stopBGM])

  // 键盘控制
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (
        event.code === 'Space' &&
        roomState.gameState.gamePhase === 'playing'
      ) {
        event.preventDefault()
        // 更新当前玩家的嘴巴状态
        if (currentPlayer) {
          setRoomState(prev => ({
            ...prev,
            players: prev.players.map(p =>
              p.id === playerId ? { ...p, isPlayerMouthOpen: true } : p
            )
          }))

          // 更新本地显示
          setPlayPersons(prev =>
            prev.map((person, index) =>
              index === currentPlayer.playerIndex
                ? { ...person, isMouthOpen: true }
                : person
            )
          )
        }
      }
    }

    const handleKeyUp = (event: KeyboardEvent) => {
      if (
        event.code === 'Space' &&
        roomState.gameState.gamePhase === 'playing'
      ) {
        event.preventDefault()
        // 更新当前玩家的嘴巴状态
        if (currentPlayer) {
          setRoomState(prev => ({
            ...prev,
            players: prev.players.map(p =>
              p.id === playerId ? { ...p, isPlayerMouthOpen: false } : p
            )
          }))

          // 更新本地显示
          setPlayPersons(prev =>
            prev.map((person, index) =>
              index === currentPlayer.playerIndex
                ? { ...person, isMouthOpen: false }
                : person
            )
          )
        }
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    window.addEventListener('keyup', handleKeyUp)

    return () => {
      window.removeEventListener('keydown', handleKeyDown)
      window.removeEventListener('keyup', handleKeyUp)
    }
  }, [roomState.gameState.gamePhase, currentPlayer, playerId])

  // 加入游戏
  const joinGame = () => {
    console.log('=== 联机游戏加入 ===')
    console.log('playerName:', playerName)
    console.log('isRoomFull:', isRoomFull)
    console.log('roomState:', roomState)
    console.log('availableHorseIndices:', roomState?.availableHorseIndices)

    if (!playerName.trim()) {
      alert('请输入玩家名称')
      return
    }

    if (isRoomFull) {
      alert('房间已满')
      return
    }

    if (
      !roomState.availableHorseIndices ||
      roomState.availableHorseIndices.length === 0
    ) {
      alert('没有可用的小马位置')
      return
    }

    // 随机选择一个可用的小马索引
    const randomIndex = Math.floor(
      Math.random() * roomState.availableHorseIndices.length
    )
    const assignedHorseIndex = roomState.availableHorseIndices[randomIndex]
    console.log('分配的小马索引:', assignedHorseIndex)

    const newPlayer: PlayerState = {
      id: playerId,
      name: playerName,
      lives: 3,
      score: 0,
      playerIndex: assignedHorseIndex,
      isPlayerMouthOpen: false,
      isReady: false
    }

    setRoomState(prev => ({
      ...prev,
      players: [...prev.players, newPlayer],
      availableHorseIndices: (prev.availableHorseIndices || []).filter(
        index => index !== assignedHorseIndex
      )
    }))
    setIsJoined(true)
  }

  // 离开游戏
  const leaveGame = () => {
    if (!currentPlayer) return

    setRoomState(prev => ({
      ...prev,
      players: prev.players.filter(p => p.id !== playerId),
      availableHorseIndices: [
        ...(prev.availableHorseIndices || []),
        currentPlayer.playerIndex
      ].sort()
    }))
    setIsJoined(false)
  }

  // 准备/取消准备
  const toggleReady = () => {
    if (!currentPlayer) return

    setRoomState(prev => ({
      ...prev,
      players: prev.players.map(p =>
        p.id === playerId ? { ...p, isReady: !p.isReady } : p
      )
    }))
  }

  // 开始游戏
  const startGame = async () => {
    if (roomState.players.length !== 2) return // 必须双人才能开始

    // 启动音乐
    await handleUserInteraction()

    // 初始化演示小牛马
    const currentLevel = gameLevels[0]
    const newDemoPersons = Array.from({ length: 5 }, (_, index) => ({
      index,
      isMouthOpen: false,
      isPlayer: false
    }))

    // 初始化玩家小牛马
    const newPlayPersons = Array.from({ length: 5 }, (_, index) => {
      const player = roomState.players.find(p => p.playerIndex === index)
      return {
        index,
        isMouthOpen: false,
        isPlayer: !!player,
        playerName: player?.name || '',
        isAlive: player ? player.lives > 0 : true
      }
    })

    setDemoPersons(newDemoPersons)
    setPlayPersons(newPlayPersons)

    setRoomState(prev => ({
      ...prev,
      gameState: {
        ...prev.gameState,
        gamePhase: 'demo',
        demoStartTime: Date.now(),
        currentLevel: 0
      }
    }))

    // 开始演示动画
    console.log('开始演示动画，关卡数据:', currentLevel)
    startDemoAnimation(currentLevel)
  }

  // 演示动画
  const startDemoAnimation = (level: any) => {
    console.log('startDemoAnimation 被调用，level:', level)

    if (!level || !level.actions) {
      console.error('关卡数据无效:', level)
      return
    }

    console.log('开始播放演示动画，actions:', level.actions)

    // 为每个动作创建开始和结束事件
    const events: Array<{
      time: number
      personIndex: number
      action: 'open' | 'close'
    }> = []

    level.actions.forEach((action: any) => {
      events.push({
        time: action.startTime,
        personIndex: action.personIndex,
        action: 'open'
      })
      events.push({
        time: action.startTime + action.duration,
        personIndex: action.personIndex,
        action: 'close'
      })
    })

    // 按时间排序
    events.sort((a, b) => a.time - b.time)
    console.log('生成的事件序列:', events)

    // 执行所有事件
    events.forEach((event, index) => {
      setTimeout(() => {
        console.log(`执行事件 ${index}:`, event)

        setDemoPersons(prev => {
          const newPersons = prev.map((person, i) => ({
            ...person,
            isMouthOpen:
              i === event.personIndex
                ? event.action === 'open'
                : person.isMouthOpen
          }))
          console.log('更新演示小牛马状态:', newPersons)
          return newPersons
        })
      }, event.time)
    })

    // 演示结束后开始倒计时
    setTimeout(() => {
      console.log('演示动画结束，开始倒计时')
      setRoomState(prev => ({
        ...prev,
        gameState: {
          ...prev.gameState,
          gamePhase: 'countdown',
          playStartTime: Date.now()
        }
      }))

      // 倒计时后开始游戏
      setTimeout(() => {
        console.log('倒计时结束，开始游戏')
        setRoomState(prev => ({
          ...prev,
          gameState: {
            ...prev.gameState,
            gamePhase: 'playing'
          }
        }))
      }, 1000)
    }, level.totalDuration + 500) // 演示结束后等待500ms
  }

  // 删除重复的函数定义

  // 初始化小牛马状态
  const initializePersons = () => {
    const persons = Array.from({ length: 5 }, (_, index) => ({
      index,
      isMouthOpen: false,
      isPlayer: false
    }))
    return persons
  }

  // 更新小牛马状态
  useEffect(() => {
    setDemoPersons(initializePersons())
    setPlayPersons(
      initializePersons().map((person, index) => {
        const player = roomState.players.find(p => p.playerIndex === index)
        return {
          ...person,
          isPlayer: !!player,
          playerName: player?.name || '', // 添加玩家名称
          isAlive: player ? player.lives > 0 : true, // 添加存活状态
          isMouthOpen: player ? player.isPlayerMouthOpen : false // 同步嘴巴状态
        }
      })
    )
  }, [roomState.players])

  // 游戏结果判定
  useEffect(() => {
    if (roomState.gameState.gamePhase === 'playing') {
      // 检查是否有玩家血量为0
      const deadPlayers = roomState.players.filter(p => p.lives <= 0)
      if (deadPlayers.length > 0) {
        // 有玩家死亡，游戏结束
        const alivePlayers = roomState.players.filter(p => p.lives > 0)
        setRoomState(prev => ({
          ...prev,
          gameState: {
            ...prev.gameState,
            gamePhase: 'gameOver',
            winner: alivePlayers.length > 0 ? alivePlayers[0].name : null
          }
        }))
      }
    }
  }, [roomState.players])

  // 关卡完成检查
  const checkLevelComplete = () => {
    // 这里应该根据实际游戏逻辑判断关卡是否完成
    // 暂时简化为延时自动进入下一关
    setTimeout(() => {
      // 随机让一个玩家失败（模拟游戏逻辑）
      if (Math.random() < 0.3 && roomState.players.length > 0) {
        const randomPlayer =
          roomState.players[
            Math.floor(Math.random() * roomState.players.length)
          ]
        handlePlayerFailed(randomPlayer.id)
        return
      }

      const nextLevel = roomState.gameState.currentLevel + 1
      if (nextLevel >= gameLevels.length) {
        // 游戏完成
        setRoomState(prev => ({
          ...prev,
          gameState: {
            ...prev.gameState,
            gamePhase: 'gameOver',
            winner: '双人胜利'
          }
        }))
      } else {
        // 进入下一关
        setRoomState(prev => ({
          ...prev,
          gameState: {
            ...prev.gameState,
            currentLevel: nextLevel,
            gamePhase: 'demo',
            demoStartTime: Date.now()
          }
        }))
        // 开始下一关的演示
        startDemoAnimation(gameLevels[nextLevel])
      }
    }, 3000) // 3秒后进入下一关
  }

  // 玩家失败处理
  const handlePlayerFailed = (playerId: string) => {
    setRoomState(prev => {
      const updatedPlayers = prev.players.map(p =>
        p.id === playerId ? { ...p, lives: Math.max(0, p.lives - 1) } : p
      )

      // 检查是否有玩家死亡
      const deadPlayers = updatedPlayers.filter(p => p.lives <= 0)
      const alivePlayers = updatedPlayers.filter(p => p.lives > 0)

      if (deadPlayers.length > 0) {
        // 有玩家死亡，游戏结束
        return {
          ...prev,
          players: updatedPlayers,
          gameState: {
            ...prev.gameState,
            gamePhase: 'gameOver',
            winner: alivePlayers.length > 0 ? alivePlayers[0].name : '无人获胜'
          }
        }
      }

      return {
        ...prev,
        players: updatedPlayers
      }
    })
  }

  // 监听游戏阶段变化
  useEffect(() => {
    if (roomState.gameState.gamePhase === 'playing') {
      // 开始关卡完成检查
      checkLevelComplete()
    }
  }, [roomState.gameState.gamePhase])

  // 样式定义 - 采用与单机版相似的左右布局
  const gameContainerStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'row',
    height: '100vh',
    width: '100vw',
    backgroundColor: '#f8f9fa',
    fontFamily: '"Courier New", "Monaco", "Menlo", monospace',
    overflow: 'hidden',
    position: 'relative',
    backgroundImage: `
      linear-gradient(90deg, #e9ecef 1px, transparent 1px),
      linear-gradient(180deg, #e9ecef 1px, transparent 1px)
    `,
    backgroundSize: '20px 20px'
  }

  // 左侧信息面板样式
  const leftPanelStyle: React.CSSProperties = {
    width: '350px',
    minWidth: '350px',
    height: '100vh',
    padding: '20px',
    backgroundColor: '#fff',
    border: '4px solid #000',
    borderTop: 'none',
    borderLeft: 'none',
    borderBottom: 'none',
    display: 'flex',
    flexDirection: 'column',
    gap: '20px',
    overflowY: 'auto'
  }

  // 右侧游戏区域样式
  const rightGameAreaStyle: React.CSSProperties = {
    flex: 1,
    height: '100vh',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    padding: '20px',
    position: 'relative',
    overflow: 'hidden'
  }

  const headerStyle: React.CSSProperties = {
    textAlign: 'center',
    marginBottom: '20px',
    padding: '20px',
    border: '4px solid #303030',
    borderRadius: '0',
    backgroundColor: '#f3f0ed',
    boxShadow: '8px 8px 0px #303030',
    position: 'relative'
  }

  const titleStyle: React.CSSProperties = {
    fontSize: '24px',
    fontWeight: 'bold',
    color: '#303030',
    marginBottom: '10px',
    letterSpacing: '2px',
    textTransform: 'uppercase'
  }

  const playersListStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    gap: '15px'
  }

  const playerCardStyle: React.CSSProperties = {
    border: '3px solid #303030',
    borderRadius: '0',
    padding: '15px',
    backgroundColor: '#f3f0ed',
    boxShadow: '4px 4px 0px #303030',
    marginBottom: '10px'
  }

  const readyPlayerStyle: React.CSSProperties = {
    ...playerCardStyle,
    borderColor: '#28a745',
    backgroundColor: '#d4edda',
    boxShadow: '4px 4px 0px #28a745'
  }

  const buttonStyle: React.CSSProperties = {
    padding: '12px 24px',
    fontSize: '14px',
    fontWeight: 'bold',
    border: '3px solid #303030',
    borderRadius: '0',
    cursor: 'pointer',
    backgroundColor: '#f3f0ed',
    color: '#303030',
    transition: 'all 0.1s ease',
    boxShadow: '4px 4px 0px #303030',
    textTransform: 'uppercase',
    letterSpacing: '1px',
    fontFamily: '"Courier New", "Monaco", "Menlo", monospace',
    margin: '5px'
  }

  const primaryButtonStyle: React.CSSProperties = {
    ...buttonStyle,
    backgroundColor: '#007bff',
    color: 'white',
    borderColor: '#007bff',
    boxShadow: '4px 4px 0px #007bff'
  }

  const successButtonStyle: React.CSSProperties = {
    ...buttonStyle,
    backgroundColor: '#28a745',
    color: 'white',
    borderColor: '#28a745',
    boxShadow: '4px 4px 0px #28a745'
  }

  const dangerButtonStyle: React.CSSProperties = {
    ...buttonStyle,
    backgroundColor: '#dc3545',
    color: 'white',
    borderColor: '#dc3545',
    boxShadow: '4px 4px 0px #dc3545'
  }

  const gameAreaStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: '24px',
    height: '680px',
    width: '100%',
    maxWidth: '800px',
    justifyContent: 'flex-start',
    padding: '40px 20px 20px 20px',
    border: '4px solid #303030',
    borderRadius: '0',
    backgroundColor: '#f3f0ed',
    boxShadow: '8px 8px 0px #303030',
    position: 'relative',
    overflow: 'hidden'
  }

  const instructionStyle: React.CSSProperties = {
    textAlign: 'center',
    fontSize: '16px',
    color: '#303030',
    backgroundColor: '#f3f0ed',
    padding: '16px',
    borderRadius: '0',
    border: '3px solid #303030',
    boxShadow: '6px 6px 0px #303030',
    fontFamily: '"Courier New", "Monaco", "Menlo", monospace',
    fontWeight: 'bold'
  }

  // 渲染等待界面
  if (!isJoined) {
    return (
      <div style={gameContainerStyle}>
        {/* 左侧信息面板 */}
        <div style={leftPanelStyle}>
          <div style={headerStyle}>
            <div style={titleStyle}>联机嘴炮游戏</div>
          </div>

          {/* 加入游戏区域 */}
          <div
            style={{
              backgroundColor: '#f3f0ed',
              border: '3px solid #303030',
              borderRadius: '0',
              padding: '20px',
              boxShadow: '4px 4px 0px #303030'
            }}
          >
            <h3 style={{ color: '#303030', marginBottom: '15px' }}>加入房间</h3>
            <input
              type="text"
              value={playerName}
              onChange={e => setPlayerName(e.target.value)}
              placeholder="输入你的名称"
              style={{
                width: '100%',
                padding: '12px',
                fontSize: '16px',
                border: '3px solid #303030',
                borderRadius: '0',
                marginBottom: '15px',
                fontFamily: '"Courier New", "Monaco", "Menlo", monospace',
                backgroundColor: '#fff',
                boxShadow: '2px 2px 0px #303030'
              }}
            />
            <button
              onClick={joinGame}
              disabled={!playerName.trim() || isRoomFull}
              style={primaryButtonStyle}
            >
              {isRoomFull ? '房间已满' : '加入游戏'}
            </button>
            <button
              onClick={() => {
                console.log('重置联机房间状态')
                setRoomState({
                  players: [],
                  gameState: {
                    currentLevel: 0,
                    gamePhase: 'waiting',
                    demoStartTime: 0,
                    playStartTime: 0,
                    currentTime: 0
                  },
                  maxPlayers: 2,
                  availableHorseIndices: [0, 1, 2, 3, 4]
                })
                alert('房间状态已重置!')
              }}
              style={dangerButtonStyle}
            >
              重置房间
            </button>
          </div>

          {/* 玩家列表 */}
          <div style={playersListStyle}>
            <h3 style={{ color: '#303030', marginBottom: '15px' }}>在线玩家</h3>
            {roomState.players.map(player => (
              <div
                key={player.id}
                style={player.isReady ? readyPlayerStyle : playerCardStyle}
              >
                <h4 style={{ color: '#303030' }}>
                  🐴 {player.name} (小马 {player.playerIndex + 1})
                </h4>
                <p style={{ color: '#303030' }}>生命: ❤️ {player.lives}</p>
                <p style={{ color: '#303030' }}>积分: ⭐ {player.score}</p>
                <p style={{ color: '#303030' }}>
                  状态: {player.isReady ? '✅ 已准备' : '⏳ 等待中'}
                </p>
                {player.lives === 0 && (
                  <p style={{ color: '#dc3545' }}>💀 已淘汰</p>
                )}
              </div>
            ))}

            {/* 显示空位 */}
            {roomState.players.length < roomState.maxPlayers && (
              <div style={{ ...playerCardStyle, opacity: 0.5 }}>
                <h4 style={{ color: '#303030' }}>
                  空位 {roomState.players.length + 1}
                </h4>
                <p style={{ color: '#303030' }}>等待玩家加入...</p>
              </div>
            )}
          </div>

          {/* 房间信息 */}
          <div
            style={{
              backgroundColor: '#f3f0ed',
              border: '3px solid #303030',
              borderRadius: '0',
              padding: '15px',
              boxShadow: '4px 4px 0px #303030'
            }}
          >
            <h3 style={{ color: '#303030', marginBottom: '10px' }}>房间信息</h3>
            <p style={{ color: '#303030', fontSize: '14px' }}>
              <strong>双人游戏模式</strong>
            </p>
            <p style={{ color: '#303030', fontSize: '14px' }}>
              房间人数: {roomState.players.length} / {roomState.maxPlayers}
            </p>
            <p style={{ color: '#303030', fontSize: '14px' }}>
              准备人数: {roomState.players.filter(p => p.isReady).length} /{' '}
              {roomState.players.length}
            </p>
            <p style={{ color: '#303030', fontSize: '12px' }}>
              可用位置:{' '}
              {roomState.availableHorseIndices
                ? roomState.availableHorseIndices.join(', ')
                : '加载中...'}
            </p>
            {roomState.players.length < 2 && (
              <p
                style={{
                  color: '#f59e0b',
                  fontSize: '14px',
                  fontWeight: 'bold'
                }}
              >
                ⚠️ 需要2名玩家才能开始游戏
              </p>
            )}
          </div>
        </div>

        {/* 右侧游戏预览区域 */}
        <div style={rightGameAreaStyle}>
          <div style={gameAreaStyle}>
            <div style={instructionStyle}>
              <h2 style={{ color: '#303030', marginBottom: '15px' }}>
                欢迎来到联机嘴炮游戏！
              </h2>
              <p style={{ color: '#303030' }}>游戏规则：</p>
              <p style={{ color: '#303030' }}>1. 观看第一排小牛马的示例表演</p>
              <p style={{ color: '#303030' }}>2. 倒计时后，第二排开始模仿</p>
              <p style={{ color: '#303030' }}>
                3. 按住空格键控制你的小牛马张嘴
              </p>
              <p style={{ color: '#303030' }}>
                4. 跟随示例的节奏，准确模仿张嘴时机和时长
              </p>
              <p style={{ color: '#303030' }}>
                5. 双人必须都通过才能进入下一关
              </p>
              <p style={{ color: '#303030' }}>
                6. 每个玩家有3次生命，出错会扣除生命
              </p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // 渲染游戏界面
  return (
    <div style={gameContainerStyle}>
      {/* 左侧信息面板 */}
      <div style={leftPanelStyle}>
        <div style={headerStyle}>
          <div style={titleStyle}>联机嘴炮游戏</div>
        </div>

        {/* 玩家状态栏 */}
        <div style={playersListStyle}>
          <h3 style={{ color: '#303030', marginBottom: '15px' }}>游戏中玩家</h3>
          {roomState.players.map(player => (
            <div
              key={player.id}
              style={player.isReady ? readyPlayerStyle : playerCardStyle}
            >
              <h4 style={{ color: '#303030' }}>
                🐴 {player.name} (小马 {player.playerIndex + 1}){' '}
                {player.id === playerId && '(你)'}
              </h4>
              <p style={{ color: '#303030' }}>生命: ❤️ {player.lives}</p>
              <p style={{ color: '#303030' }}>积分: ⭐ {player.score}</p>
              <p style={{ color: '#303030' }}>
                称号: {getTitleByScore(player.score)}
              </p>
              <p style={{ color: '#303030' }}>
                状态: {player.isReady ? '✅ 已准备' : '⏳ 等待中'}
              </p>
              {player.lives === 0 && (
                <p style={{ color: '#dc3545' }}>💀 已淘汰</p>
              )}
            </div>
          ))}
        </div>

        {/* 游戏控制按钮 */}
        <div
          style={{
            backgroundColor: '#f3f0ed',
            border: '3px solid #303030',
            borderRadius: '0',
            padding: '15px',
            boxShadow: '4px 4px 0px #303030'
          }}
        >
          <h3 style={{ color: '#303030', marginBottom: '15px' }}>游戏控制</h3>
          {currentPlayer && (
            <button
              onClick={toggleReady}
              style={
                currentPlayer.isReady ? dangerButtonStyle : successButtonStyle
              }
            >
              {currentPlayer.isReady ? '取消准备' : '准备'}
            </button>
          )}

          {roomState.players.every(p => p.isReady) &&
            roomState.players.length === 2 && (
              <button onClick={startGame} style={primaryButtonStyle}>
                开始游戏
              </button>
            )}

          <button onClick={leaveGame} style={dangerButtonStyle}>
            离开房间
          </button>
        </div>
      </div>

      {/* 右侧游戏区域 */}
      <div style={rightGameAreaStyle}>
        {roomState.gameState.gamePhase === 'gameOver' ? (
          <div style={gameAreaStyle}>
            <div style={instructionStyle}>
              <h2 style={{ color: '#303030', marginBottom: '15px' }}>
                🎉 游戏结束！
              </h2>
              <p
                style={{
                  color: '#303030',
                  fontSize: '18px',
                  marginBottom: '10px'
                }}
              >
                获胜者: {(roomState.gameState as any).winner || '无'}
              </p>
              <button
                onClick={() => {
                  setRoomState(prev => ({
                    ...prev,
                    gameState: {
                      ...prev.gameState,
                      gamePhase: 'waiting',
                      currentLevel: 0
                    },
                    players: prev.players.map(p => ({
                      ...p,
                      lives: 3,
                      score: 0,
                      isReady: false
                    }))
                  }))
                }}
                style={primaryButtonStyle}
              >
                重新开始
              </button>
            </div>
          </div>
        ) : roomState.gameState.gamePhase !== 'waiting' ? (
          <div style={gameAreaStyle}>
            <div style={{ textAlign: 'center', marginBottom: '20px' }}>
              <h3 style={{ color: '#303030' }}>
                关卡 {roomState.gameState.currentLevel + 1} /{' '}
                {gameLevels.length}
              </h3>
              <p style={{ color: '#303030' }}>
                游戏状态: {roomState.gameState.gamePhase}
              </p>
            </div>

            <PersonRow persons={demoPersons} label="示例表演" />
            <PersonRow persons={playPersons} label="模仿表演" />

            <div style={instructionStyle}>
              {roomState.gameState.gamePhase === 'demo' && (
                <p>请仔细观看示例表演，记住节奏！</p>
              )}
              {roomState.gameState.gamePhase === 'countdown' && (
                <p>准备好！按住空格键控制你的小牛马张嘴</p>
              )}
              {roomState.gameState.gamePhase === 'playing' && (
                <p>按住空格键控制你的小牛马张嘴！</p>
              )}
            </div>
          </div>
        ) : (
          <div style={gameAreaStyle}>
            <div style={instructionStyle}>
              <h2 style={{ color: '#303030', marginBottom: '15px' }}>
                等待游戏开始
              </h2>
              <p style={{ color: '#303030' }}>游戏规则：</p>
              <p style={{ color: '#303030' }}>1. 观看第一排小牛马的示例表演</p>
              <p style={{ color: '#303030' }}>2. 倒计时后，第二排开始模仿</p>
              <p style={{ color: '#303030' }}>
                3. 按住空格键控制你的小牛马张嘴
              </p>
              <p style={{ color: '#303030' }}>
                4. 跟随示例的节奏，准确模仿张嘴时机和时长
              </p>
              <p style={{ color: '#303030' }}>
                5. 双人必须都通过才能进入下一关
              </p>
              <p style={{ color: '#303030' }}>
                6. 每个玩家有3次生命，出错会扣除生命
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

// 使用 ReactTogether 包装的组件
function MultiplayerMusicGame() {
  return (
    <ReactTogether
      sessionParams={{
        appId: 'com.mouthgame.multiplayer',
        apiKey: '2b599LeFw5HNytHLfolNXnHvK7VkXFdUm23jxmC2Z3',
        name: 'mouthgame-room',
        password: 'game123'
      }}
    >
      <MultiplayerMusicGameContent />
    </ReactTogether>
  )
}

export default MultiplayerMusicGame
