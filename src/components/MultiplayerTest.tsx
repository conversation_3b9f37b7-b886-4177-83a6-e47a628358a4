import React, { useState } from 'react'
import { ReactTogether, useSharedState } from '@croquet/react'

// 简单的联机测试组件
function MultiplayerTestContent() {
  const [message, setMessage] = useState('')
  const [playerName, setPlayerName] = useState('')

  // 使用共享状态来同步消息
  const [messages, setMessages] = useSharedState<string[]>('messages', [])
  const [connectedPlayers, setConnectedPlayers] = useSharedState<string[]>(
    'players',
    []
  )

  // 发送消息到所有连接的客户端
  const sendMessage = () => {
    if (message.trim() && playerName.trim()) {
      const newMessage = `${playerName}: ${message}`
      setMessages(prev => [...prev, newMessage])
      setMessage('')
    }
  }

  // 加入游戏
  const joinGame = () => {
    if (playerName.trim() && !connectedPlayers.includes(playerName)) {
      setConnectedPlayers(prev => [...prev, playerName])
      setMessages(prev => [...prev, `${playerName} 加入了游戏`])
    }
  }

  // 离开游戏
  const leaveGame = () => {
    if (playerName.trim() && connectedPlayers.includes(playerName)) {
      setConnectedPlayers(prev => prev.filter(p => p !== playerName))
      setMessages(prev => [...prev, `${playerName} 离开了游戏`])
    }
  }

  const containerStyle: React.CSSProperties = {
    padding: '20px',
    maxWidth: '600px',
    margin: '0 auto',
    fontFamily: 'Arial, sans-serif'
  }

  const headerStyle: React.CSSProperties = {
    textAlign: 'center',
    color: '#333',
    marginBottom: '20px'
  }

  const inputGroupStyle: React.CSSProperties = {
    marginBottom: '15px',
    display: 'flex',
    gap: '10px',
    alignItems: 'center'
  }

  const inputStyle: React.CSSProperties = {
    padding: '8px 12px',
    border: '1px solid #ddd',
    borderRadius: '4px',
    fontSize: '14px'
  }

  const buttonStyle: React.CSSProperties = {
    padding: '8px 16px',
    backgroundColor: '#007bff',
    color: 'white',
    border: 'none',
    borderRadius: '4px',
    cursor: 'pointer',
    fontSize: '14px'
  }

  const messagesStyle: React.CSSProperties = {
    border: '1px solid #ddd',
    borderRadius: '4px',
    padding: '15px',
    minHeight: '200px',
    backgroundColor: '#f9f9f9',
    marginBottom: '15px'
  }

  const messageStyle: React.CSSProperties = {
    marginBottom: '8px',
    padding: '5px',
    backgroundColor: 'white',
    borderRadius: '3px',
    border: '1px solid #eee'
  }

  const isPlayerJoined = connectedPlayers.includes(playerName)

  return (
    <div style={containerStyle}>
      <h1 style={headerStyle}>联机测试 - 嘴炮游戏</h1>

      <div style={inputGroupStyle}>
        <label>玩家名称:</label>
        <input
          type="text"
          value={playerName}
          onChange={e => setPlayerName(e.target.value)}
          placeholder="输入你的名称"
          style={inputStyle}
          disabled={isPlayerJoined}
        />
        {!isPlayerJoined ? (
          <button
            onClick={joinGame}
            style={buttonStyle}
            disabled={!playerName.trim()}
          >
            加入游戏
          </button>
        ) : (
          <button
            onClick={leaveGame}
            style={{ ...buttonStyle, backgroundColor: '#dc3545' }}
          >
            离开游戏
          </button>
        )}
      </div>

      <div style={{ display: 'flex', gap: '20px', marginBottom: '20px' }}>
        <div style={{ flex: 1 }}>
          <h3>在线玩家 ({connectedPlayers.length}):</h3>
          <div style={{ ...messagesStyle, minHeight: '100px' }}>
            {connectedPlayers.length === 0 ? (
              <p style={{ color: '#666' }}>暂无玩家在线</p>
            ) : (
              connectedPlayers.map((player, index) => (
                <div
                  key={index}
                  style={{ ...messageStyle, backgroundColor: '#e3f2fd' }}
                >
                  🎮 {player}
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      <div style={messagesStyle}>
        <h3>消息列表:</h3>
        {messages.length === 0 ? (
          <p style={{ color: '#666' }}>还没有消息...</p>
        ) : (
          messages.map((msg, index) => (
            <div key={index} style={messageStyle}>
              {msg}
            </div>
          ))
        )}
      </div>

      {isPlayerJoined && (
        <div style={inputGroupStyle}>
          <input
            type="text"
            value={message}
            onChange={e => setMessage(e.target.value)}
            placeholder="输入消息..."
            style={{ ...inputStyle, flex: 1 }}
            onKeyPress={e => e.key === 'Enter' && sendMessage()}
          />
          <button
            onClick={sendMessage}
            style={buttonStyle}
            disabled={!message.trim()}
          >
            发送
          </button>
        </div>
      )}

      <div
        style={{
          marginTop: '20px',
          padding: '10px',
          backgroundColor: '#e8f5e8',
          borderRadius: '4px'
        }}
      >
        <p>
          <strong>测试说明:</strong>
        </p>
        <p>1. 输入你的玩家名称并点击"加入游戏"</p>
        <p>2. 发送消息测试实时同步</p>
        <p>3. 在多个浏览器窗口中打开此页面测试多人同步</p>
        <p>4. 观察玩家列表和消息的实时更新</p>
      </div>
    </div>
  )
}

// 使用 ReactTogether 包装的组件
function MultiplayerTest() {
  return (
    <ReactTogether
      sessionParams={{
        appId: 'com.mouthgame.multiplayer',
        apiKey: '2b599LeFw5HNytHLfolNXnHvK7VkXFdUm23jxmC2Z3',
        name: 'mouthgame-test',
        password: 'test123'
      }}
    >
      <MultiplayerTestContent />
    </ReactTogether>
  )
}

export default MultiplayerTest
