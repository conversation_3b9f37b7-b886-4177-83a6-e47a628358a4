import React, { useState } from 'react'
import { ReactTogether, useStateTogether } from 'react-together'

function ReactTogetherTestContent() {
  const [localInput, setLocalInput] = useState('')

  // 测试基本的状态同步
  const [sharedCounter, setSharedCounter] = useStateTogether('counter', 0)
  const [sharedMessage, setSharedMessage] = useStateTogether(
    'message',
    'Hello World'
  )
  const [sharedArray, setSharedArray] = useStateTogether(
    'array',
    [1, 2, 3, 4, 5]
  )

  const containerStyle: React.CSSProperties = {
    padding: '20px',
    maxWidth: '800px',
    margin: '0 auto',
    fontFamily: 'Arial, sans-serif'
  }

  const buttonStyle: React.CSSProperties = {
    padding: '10px 20px',
    margin: '5px',
    backgroundColor: '#007bff',
    color: 'white',
    border: 'none',
    borderRadius: '5px',
    cursor: 'pointer',
    fontSize: '14px'
  }

  const debugStyle: React.CSSProperties = {
    backgroundColor: '#e8f5e9', // 浅绿色背景
    border: '1px solid #c8e6c9',
    borderRadius: '5px',
    padding: '15px',
    marginBottom: '20px',
    fontSize: '12px',
    fontFamily: 'monospace',
    color: '#2e7d32'
  }

  return (
    <div style={containerStyle}>
      <h1>ReactTogether 基础测试</h1>

      {/* 调试信息 */}
      <div style={debugStyle}>
        <h3>当前状态:</h3>
        <p>
          <strong>计数器:</strong> {sharedCounter}
        </p>
        <p>
          <strong>消息:</strong> {sharedMessage}
        </p>
        <p>
          <strong>数组:</strong> [{sharedArray.join(', ')}]
        </p>
        <p>
          <strong>数组长度:</strong> {sharedArray.length}
        </p>
        <p>
          <strong>数组类型:</strong>{' '}
          {Array.isArray(sharedArray) ? 'Array' : typeof sharedArray}
        </p>
      </div>

      {/* 计数器测试 */}
      <div style={{ marginBottom: '20px' }}>
        <h3>计数器测试:</h3>
        <button
          onClick={() => setSharedCounter(sharedCounter + 1)}
          style={buttonStyle}
        >
          +1
        </button>
        <button
          onClick={() => setSharedCounter(sharedCounter - 1)}
          style={buttonStyle}
        >
          -1
        </button>
        <button
          onClick={() => setSharedCounter(0)}
          style={{ ...buttonStyle, backgroundColor: '#dc3545' }}
        >
          重置
        </button>
      </div>

      {/* 消息测试 */}
      <div style={{ marginBottom: '20px' }}>
        <h3>消息测试:</h3>
        <input
          type="text"
          value={localInput}
          onChange={e => setLocalInput(e.target.value)}
          placeholder="输入消息"
          style={{
            padding: '8px',
            marginRight: '10px',
            border: '1px solid #ddd',
            borderRadius: '4px'
          }}
        />
        <button
          onClick={() => {
            setSharedMessage(localInput)
            setLocalInput('')
          }}
          style={buttonStyle}
        >
          更新消息
        </button>
      </div>

      {/* 数组测试 */}
      <div style={{ marginBottom: '20px' }}>
        <h3>数组测试:</h3>
        <button
          onClick={() => {
            const newNumber = Math.floor(Math.random() * 10)
            setSharedArray([...sharedArray, newNumber])
          }}
          style={buttonStyle}
        >
          添加随机数
        </button>
        <button
          onClick={() => {
            if (sharedArray.length > 0) {
              setSharedArray(sharedArray.slice(0, -1))
            }
          }}
          style={buttonStyle}
        >
          删除最后一个
        </button>
        <button
          onClick={() => setSharedArray([1, 2, 3, 4, 5])}
          style={{ ...buttonStyle, backgroundColor: '#dc3545' }}
        >
          重置数组
        </button>
      </div>

      {/* 测试说明 */}
      <div
        style={{
          backgroundColor: '#e1f5fe', // 浅蓝色背景
          border: '1px solid #b3e5fc',
          borderRadius: '5px',
          padding: '15px',
          marginTop: '20px'
        }}
      >
        <h3 style={{ color: '#01579b' }}>测试说明:</h3>
        <p style={{ color: '#01579b' }}>1. 在多个浏览器窗口中打开此页面</p>
        <p style={{ color: '#01579b' }}>
          2. 操作计数器、消息、数组，观察是否实时同步
        </p>
        <p style={{ color: '#01579b' }}>
          3. 如果同步正常，说明 ReactTogether 工作正常
        </p>
        <p style={{ color: '#01579b' }}>
          4. 如果数组显示为空或不同步，说明有问题
        </p>
      </div>

      {/* 原始数据显示 */}
      <div style={debugStyle}>
        <h3>原始数据 (JSON):</h3>
        <pre>
          {JSON.stringify(
            {
              counter: sharedCounter,
              message: sharedMessage,
              array: sharedArray
            },
            null,
            2
          )}
        </pre>
      </div>
    </div>
  )
}

function ReactTogetherTest() {
  return (
    <ReactTogether
      sessionParams={{
        appId: 'com.mouthgame.basic-test',
        apiKey: '2b599LeFw5HNytHLfolNXnHvK7VkXFdUm23jxmC2Z3',
        name: 'basic-test-room'
      }}
    >
      <ReactTogetherTestContent />
    </ReactTogether>
  )
}

export default ReactTogetherTest
