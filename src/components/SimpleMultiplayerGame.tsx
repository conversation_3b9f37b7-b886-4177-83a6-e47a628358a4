import React, { useState } from 'react'
import { ReactTogether, useStateTogether } from 'react-together'

// 简化的玩家状态
interface SimplePlayer {
  id: string
  name: string
  horseIndex: number
}

// 简化的房间状态
interface SimpleRoomState {
  players: SimplePlayer[]
  availableHorses: number[]
}

function SimpleMultiplayerGameContent() {
  const [playerName, setPlayerName] = useState('')
  const [playerId] = useState(() => Math.random().toString(36).substring(2, 11))

  // 简化的共享状态
  const [roomState, setRoomState] = useStateTogether<SimpleRoomState>(
    'debugRoomState',
    {
      players: [],
      availableHorses: [0, 1, 2, 3, 4]
    }
  )

  const [isJoined, setIsJoined] = useState(false)

  // 获取当前玩家
  const currentPlayer = roomState.players.find(p => p.id === playerId)

  // 加入游戏
  const joinGame = () => {
    console.log('=== 开始加入游戏 ===')
    console.log('playerName:', playerName)
    console.log('playerId:', playerId)
    console.log('roomState:', roomState)
    console.log('availableHorses:', roomState?.availableHorses)

    try {
      if (!playerName.trim()) {
        console.log('错误: 玩家名称为空')
        alert('请输入玩家名称')
        return
      }

      if (!roomState) {
        console.log('错误: roomState 为空')
        alert('房间状态未加载')
        return
      }

      if (
        !roomState.availableHorses ||
        roomState.availableHorses.length === 0
      ) {
        console.log('错误: 没有可用的小马位置')
        alert('没有可用的小马位置')
        return
      }

      if (roomState.players.length >= 2) {
        console.log('错误: 房间已满')
        alert('房间已满')
        return
      }

      // 随机选择一个小马
      const randomIndex = Math.floor(
        Math.random() * roomState.availableHorses.length
      )
      const selectedHorse = roomState.availableHorses[randomIndex]

      const newPlayer: SimplePlayer = {
        id: playerId,
        name: playerName,
        horseIndex: selectedHorse
      }

      console.log('创建新玩家:', newPlayer)
      console.log('更新前的状态:', roomState)

      setRoomState(prev => {
        const newState = {
          players: [...prev.players, newPlayer],
          availableHorses: prev.availableHorses.filter(h => h !== selectedHorse)
        }
        console.log('更新后的状态:', newState)
        return newState
      })

      console.log('设置 isJoined 为 true')
      setIsJoined(true)
      console.log('=== 加入游戏完成 ===')
    } catch (error) {
      console.error('加入游戏时发生错误:', error)
      alert('加入游戏失败: ' + error)
    }
  }

  // 离开游戏
  const leaveGame = () => {
    if (!currentPlayer) return

    setRoomState(prev => ({
      players: prev.players.filter(p => p.id !== playerId),
      availableHorses: [
        ...prev.availableHorses,
        currentPlayer.horseIndex
      ].sort()
    }))

    setIsJoined(false)
  }

  const containerStyle: React.CSSProperties = {
    padding: '20px',
    maxWidth: '800px',
    margin: '0 auto',
    fontFamily: 'Arial, sans-serif'
  }

  const buttonStyle: React.CSSProperties = {
    padding: '10px 20px',
    margin: '10px',
    backgroundColor: '#007bff',
    color: 'white',
    border: 'none',
    borderRadius: '5px',
    cursor: 'pointer',
    fontSize: '16px'
  }

  const playerCardStyle: React.CSSProperties = {
    border: '2px solid #ddd',
    borderRadius: '8px',
    padding: '15px',
    margin: '10px',
    backgroundColor: '#e3f2fd' // 浅蓝色背景
  }

  return (
    <div style={containerStyle}>
      <h1>简化联机游戏测试</h1>

      {/* 调试信息 */}
      <div
        style={{
          backgroundColor: '#fff3cd', // 浅黄色背景
          border: '1px solid #ffeaa7',
          padding: '10px',
          marginBottom: '20px',
          fontSize: '12px',
          borderRadius: '5px'
        }}
      >
        <h3 style={{ color: '#000' }}>调试信息:</h3>
        <p style={{ color: '#000' }}>
          房间状态: {JSON.stringify(roomState, null, 2)}
        </p>
        <p style={{ color: '#000' }}>当前玩家ID: {playerId}</p>
        <p style={{ color: '#000' }}>是否已加入: {isJoined ? '是' : '否'}</p>
      </div>

      {!isJoined ? (
        <div>
          <h2>加入游戏</h2>
          <div style={{ marginBottom: '20px' }}>
            <input
              type="text"
              value={playerName}
              onChange={e => setPlayerName(e.target.value)}
              placeholder="输入你的名称"
              style={{
                padding: '10px',
                fontSize: '16px',
                border: '2px solid #ddd',
                borderRadius: '5px',
                marginRight: '10px'
              }}
            />
            <button
              onClick={() => {
                console.log('按钮被点击了!')
                joinGame()
              }}
              style={buttonStyle}
            >
              加入游戏
            </button>

            <button
              onClick={() => {
                console.log('测试按钮被点击!')
                alert('测试按钮工作正常!')
              }}
              style={{ ...buttonStyle, backgroundColor: '#28a745' }}
            >
              测试按钮
            </button>

            <button
              onClick={() => {
                console.log('重置房间状态')
                setRoomState({
                  players: [],
                  availableHorses: [0, 1, 2, 3, 4]
                })
                alert('房间状态已重置!')
              }}
              style={{ ...buttonStyle, backgroundColor: '#dc3545' }}
            >
              重置房间
            </button>
          </div>
        </div>
      ) : (
        <div>
          <h2>游戏房间</h2>
          <button
            onClick={leaveGame}
            style={{ ...buttonStyle, backgroundColor: '#dc3545' }}
          >
            离开游戏
          </button>
        </div>
      )}

      {/* 玩家列表 */}
      <div>
        <h3>在线玩家 ({roomState.players.length}/2):</h3>
        {roomState.players.map(player => (
          <div key={player.id} style={playerCardStyle}>
            <h4>
              🐴 {player.name} (小马 {player.horseIndex + 1})
            </h4>
            {player.id === playerId && (
              <p style={{ color: '#000', fontWeight: 'bold' }}>这是你</p>
            )}
          </div>
        ))}

        {roomState.players.length === 0 && <p>暂无玩家在线</p>}
      </div>

      {/* 可用位置 */}
      <div style={{ marginTop: '20px' }}>
        <h3>可用小马位置:</h3>
        <p>
          {roomState.availableHorses
            ? roomState.availableHorses.map(h => `小马${h + 1}`).join(', ')
            : '无'}
        </p>
      </div>

      {/* 测试说明 */}
      <div
        style={{
          marginTop: '30px',
          padding: '15px',
          backgroundColor: '#e8f5e8',
          borderRadius: '5px'
        }}
      >
        <h3>测试说明:</h3>
        <p>1. 输入玩家名称并点击"加入游戏"</p>
        <p>2. 在多个浏览器窗口中测试多人加入</p>
        <p>3. 观察小马位置的随机分配</p>
        <p>4. 测试离开游戏功能</p>
        <p>5. 查看调试信息了解状态同步</p>
      </div>
    </div>
  )
}

// 使用 ReactTogether 包装的组件
function SimpleMultiplayerGame() {
  return (
    <ReactTogether
      sessionParams={{
        appId: 'com.mouthgame.simple-multiplayer',
        apiKey: '2b599LeFw5HNytHLfolNXnHvK7VkXFdUm23jxmC2Z3',
        name: 'simple-multiplayer-room',
        password: 'simple123'
      }}
    >
      <SimpleMultiplayerGameContent />
    </ReactTogether>
  )
}

export default SimpleMultiplayerGame
