import React from 'react'
import { ReactTogether, useStateTogether } from 'react-together'

// 简单的计数器测试组件
function SimpleCounterContent() {
  const [count, setCount] = useStateTogether('counter', 0)

  const containerStyle: React.CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: '100vh',
    fontFamily: 'Arial, sans-serif',
    backgroundColor: '#f0f9ff'
  }

  const buttonStyle: React.CSSProperties = {
    padding: '15px 30px',
    fontSize: '18px',
    fontWeight: 'bold',
    backgroundColor: '#3b82f6',
    color: 'white',
    border: 'none',
    borderRadius: '8px',
    cursor: 'pointer',
    margin: '10px'
  }

  const countStyle: React.CSSProperties = {
    fontSize: '48px',
    fontWeight: 'bold',
    color: '#1e40af',
    margin: '20px'
  }

  return (
    <div style={containerStyle}>
      <h1>简单联机测试</h1>
      <p>这是一个同步计数器，在多个浏览器窗口中测试</p>

      <div style={countStyle}>计数: {count}</div>

      <div>
        <button style={buttonStyle} onClick={() => setCount(count + 1)}>
          +1
        </button>

        <button style={buttonStyle} onClick={() => setCount(count - 1)}>
          -1
        </button>

        <button
          style={{ ...buttonStyle, backgroundColor: '#dc3545' }}
          onClick={() => setCount(0)}
        >
          重置
        </button>
      </div>

      <div
        style={{
          marginTop: '30px',
          textAlign: 'center',
          maxWidth: '500px',
          backgroundColor: '#f3e5f5', // 浅紫色背景
          border: '1px solid #e1bee7',
          borderRadius: '8px',
          padding: '20px'
        }}
      >
        <h3 style={{ color: '#4a148c' }}>测试说明:</h3>
        <p style={{ color: '#4a148c' }}>1. 在多个浏览器窗口中打开此页面</p>
        <p style={{ color: '#4a148c' }}>
          2. 点击按钮，观察计数器在所有窗口中同步更新
        </p>
        <p style={{ color: '#4a148c' }}>
          3. 如果同步正常，说明 ReactTogether 连接成功
        </p>
      </div>
    </div>
  )
}

// 使用 ReactTogether 包装的组件
function SimpleMultiplayerTest() {
  return (
    <ReactTogether
      sessionParams={{
        appId: 'com.mouthgame.simple',
        apiKey: '2b599LeFw5HNytHLfolNXnHvK7VkXFdUm23jxmC2Z3',
        name: 'simple-test'
      }}
    >
      <SimpleCounterContent />
    </ReactTogether>
  )
}

export default SimpleMultiplayerTest
